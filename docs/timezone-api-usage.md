# Google Maps时区API Java实现

这是将C#的Google Maps时区API代码转换为Java版本的实现。该实现提供了通过经纬度坐标获取时区信息的功能，并将IANA时区ID转换为Windows时区ID。

## 主要组件

### 1. ITimeZoneApi 接口
定义了时区API的基本契约，提供获取时区信息的方法。

```java
public interface ITimeZoneApi {
    String getTimeZone(String lat, String lon) throws IOException;
}
```

### 2. GoogleMapsTimeZoneApi 实现类
实现了ITimeZoneApi接口，使用Google Maps Timezone API获取时区信息。

**主要特性：**
- 使用OkHttp进行HTTP请求
- 使用FastJSON进行JSON解析
- 自动将IANA时区ID转换为Windows时区ID
- 完善的错误处理

### 3. GoogleMapsTimeZoneApiResponse 响应模型
映射Google Maps Timezone API的JSON响应结构。

### 4. IanaToWindowsConverter 时区转换器
提供IANA时区ID到Windows时区ID的转换功能，支持常用的时区映射。

## 使用方法

### 基本用法

```java
// 创建API客户端（需要Google Maps API密钥）
String apiKey = "YOUR_GOOGLE_MAPS_API_KEY";
GoogleMapsTimeZoneApi timeZoneApi = new GoogleMapsTimeZoneApi(apiKey);

// 获取时区信息
try {
    String windowsTimeZone = timeZoneApi.getTimeZone("40.7128", "-74.0060");
    System.out.println("Windows时区ID: " + windowsTimeZone);
} catch (IOException e) {
    System.err.println("获取时区失败: " + e.getMessage());
}
```

### 自定义HTTP客户端

```java
// 使用自定义的OkHttpClient
OkHttpClient customClient = new OkHttpClient.Builder()
    .connectTimeout(30, TimeUnit.SECONDS)
    .readTimeout(30, TimeUnit.SECONDS)
    .build();

GoogleMapsTimeZoneApi timeZoneApi = new GoogleMapsTimeZoneApi(apiKey, customClient);
```

### 直接使用时区转换器

```java
// IANA到Windows时区转换
String windowsTimeZone = IanaToWindowsConverter.getWindowsIdForIana("America/New_York");
System.out.println(windowsTimeZone); // 输出: Eastern Standard Time

// 检查是否支持某个时区
boolean isSupported = IanaToWindowsConverter.isSupported("Asia/Shanghai");
System.out.println(isSupported); // 输出: true

// 获取所有支持的时区
Set<String> supportedTimeZones = IanaToWindowsConverter.getSupportedIanaTimeZones();
```

## 依赖要求

确保项目中包含以下依赖：

```xml
<!-- OkHttp for HTTP requests -->
<dependency>
    <groupId>com.squareup.okhttp3</groupId>
    <artifactId>okhttp</artifactId>
    <version>4.10.0</version>
</dependency>

<!-- FastJSON for JSON parsing -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>fastjson</artifactId>
    <version>1.2.83</version>
</dependency>

<!-- Apache Commons Lang3 for string utilities -->
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-lang3</artifactId>
    <version>3.14.0</version>
</dependency>
```

## API密钥配置

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建或选择一个项目
3. 启用 "Maps JavaScript API" 和 "Time Zone API"
4. 创建API密钥
5. 可选：为API密钥设置使用限制

## 错误处理

该实现包含完善的错误处理：

- **网络错误**：HTTP请求失败时抛出IOException
- **API错误**：Google API返回错误状态时抛出IOException
- **参数验证**：无效参数时抛出IllegalArgumentException
- **JSON解析错误**：响应解析失败时抛出IOException

## 支持的时区

IanaToWindowsConverter目前支持100+个常用时区的转换，包括：

- 美洲时区（美国、加拿大、墨西哥、南美洲）
- 欧洲时区（西欧、中欧、东欧）
- 亚洲时区（中国、日本、韩国、东南亚、印度、中东）
- 澳洲和太平洋时区
- 非洲时区

## 与原C#代码的对应关系

| C# | Java |
|---|---|
| `ITimeZoneApi` | `ITimeZoneApi` |
| `GoogleMapsTimeZoneApi` | `GoogleMapsTimeZoneApi` |
| `HttpClient` | `OkHttpClient` |
| `JsonConvert.DeserializeObject` | `JSON.parseObject` |
| `TZConvert.IanaToWindows` | `IanaToWindowsConverter.getWindowsIdForIana` |
| `Task<string>` | `String` (同步调用) |
| `async/await` | 直接方法调用 |

## 注意事项

1. **API配额**：Google Maps Timezone API有使用限制，请注意配额管理
2. **缓存**：建议在生产环境中实现结果缓存以减少API调用
3. **错误重试**：可以考虑添加重试机制处理临时网络错误
4. **线程安全**：GoogleMapsTimeZoneApi类是线程安全的，可以在多线程环境中使用
