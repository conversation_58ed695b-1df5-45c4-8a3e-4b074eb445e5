# Google Maps时区API Java实现（同步版本）

这是将C#的Google Maps时区API代码转换为Java同步版本的实现。

## 快速开始

### 1. 创建API客户端

```java
String apiKey = "YOUR_GOOGLE_MAPS_API_KEY";
GoogleMapsTimeZoneApi timeZoneApi = new GoogleMapsTimeZoneApi(apiKey);
```

### 2. 获取时区信息

```java
try {
    // 获取纽约的时区（纬度: 40.7128, 经度: -74.0060）
    String windowsTimeZone = timeZoneApi.getTimeZone("40.7128", "-74.0060");
    System.out.println("纽约的Windows时区ID: " + windowsTimeZone);
    // 输出: Eastern Standard Time
} catch (IOException e) {
    System.err.println("获取时区失败: " + e.getMessage());
}
```

### 3. 批量获取多个城市的时区

```java
String[][] cities = {
    {"纽约", "40.7128", "-74.0060"},
    {"伦敦", "51.5074", "-0.1278"},
    {"东京", "35.6762", "139.6503"},
    {"悉尼", "-33.8688", "151.2093"}
};

for (String[] city : cities) {
    try {
        String timeZone = timeZoneApi.getTimeZone(city[1], city[2]);
        System.out.println(city[0] + " -> " + timeZone);
    } catch (IOException e) {
        System.err.println("获取" + city[0] + "时区失败: " + e.getMessage());
    }
}
```

## 主要类说明

### ITimeZoneApi
时区API接口，定义了获取时区的方法签名。

### GoogleMapsTimeZoneApi
Google Maps时区API的实现类，主要功能：
- 调用Google Maps Timezone API
- 将IANA时区ID转换为Windows时区ID
- 完善的错误处理

### IanaToWindowsConverter
IANA到Windows时区转换工具，支持100+个常用时区的转换。

```java
// 直接使用转换器
String windowsTimeZone = IanaToWindowsConverter.getWindowsIdForIana("America/New_York");
System.out.println(windowsTimeZone); // 输出: Eastern Standard Time

// 检查是否支持某个时区
boolean isSupported = IanaToWindowsConverter.isSupported("Asia/Shanghai");
System.out.println(isSupported); // 输出: true
```

## 错误处理

所有方法都会抛出适当的异常：

- `IOException`: 网络请求失败或API返回错误
- `IllegalArgumentException`: 参数验证失败

建议在调用时使用try-catch块进行错误处理。

## 依赖要求

项目需要以下依赖（通常已包含在Spring Boot项目中）：

- OkHttp 4.10.0+
- FastJSON 1.2.83+
- Apache Commons Lang3 3.14.0+

## API密钥获取

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建项目并启用 "Time Zone API"
3. 创建API密钥
4. 替换代码中的 "YOUR_GOOGLE_MAPS_API_KEY"

## 与C#版本的对应关系

| C#原版 | Java同步版 |
|--------|------------|
| `Task<string> GetTimeZoneAsync()` | `String getTimeZone() throws IOException` |
| `HttpClient` | `OkHttpClient` |
| `JsonConvert.DeserializeObject` | `JSON.parseObject` |
| `TZConvert.IanaToWindows` | `IanaToWindowsConverter.getWindowsIdForIana` |
| `async/await` | 直接方法调用 |

## 注意事项

1. **API配额限制**: Google Maps API有使用限制，请注意配额管理
2. **缓存建议**: 生产环境建议实现结果缓存以减少API调用
3. **线程安全**: 所有类都是线程安全的，可以在多线程环境中使用
4. **错误重试**: 可以考虑添加重试机制处理临时网络错误
