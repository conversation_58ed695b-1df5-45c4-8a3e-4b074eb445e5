package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.ParserConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.config.CustomBigDecimalDeserializer;
import com.ebon.energy.fms.config.CustomDoubleDeserializer;
import com.ebon.energy.fms.config.PortalConfig;
import com.ebon.energy.fms.domain.entity.ErrorMappingDO;
import com.ebon.energy.fms.domain.entity.ModelInfoDO;
import com.ebon.energy.fms.domain.entity.ProductDeviceSettingsDO;
import com.ebon.energy.fms.domain.entity.TelemetryDO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.*;
import com.ebon.energy.fms.mapper.third.ErrorMappingMapper;
import com.ebon.energy.fms.mapper.third.TelemetryMapper;
import com.ebon.energy.fms.service.factory.EnergyFlowFactory;
import com.ebon.energy.fms.service.factory.TableStoreServiceFactory;
import com.ebon.energy.fms.util.DateTimeHelper;
import com.ebon.energy.fms.util.TimeUtils;
import com.ebon.energy.fms.util.VersionParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.util.SafeAccess.*;

@Slf4j
@Service
public class TelemetryService {

    @Resource
    private TelemetryMapper telemetryMapper;

    @Resource
    private ErrorMappingMapper errorMappingMapper;

    @Resource
    private ProductService productService;

    @Resource
    private ConfigurationService configurationService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private DeviceSettingsService deviceSettingsService;

    @Resource
    private TableStoreServiceFactory tableStoreServiceFactory;

    @Resource
    private HardwareModelService hardwareModelService;

    @Resource
    private PortalConfig portalConfig;

    public TelemetryVO getTelemetry(String serialNumber) {
        LambdaQueryWrapper<TelemetryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TelemetryDO::getSerialNumber, serialNumber);
        TelemetryDO telemetryDO = telemetryMapper.selectOne(queryWrapper);
        if (telemetryDO == null) {
            return null;
        }

        TelemetryVO telemetryVO = new TelemetryVO();
        BeanUtils.copyProperties(telemetryDO, telemetryVO);
        telemetryVO.setTelemetry(telemetryDO.getRossTelemetry());
        return telemetryVO;
    }

    public DataWithLinks<InverterInfo> getLastStatus(String sn, boolean isRound) {
        ProductVO productVO = productService.getProduct(sn);
        if (productVO == null) {
            throw new BizException("The serial number doesn't exist.");
        }

        if (StringUtils.isBlank(productVO.getLatestSystemStatus())) {
            return DataWithLinks.getDataWithLinks(sn, null, getSystemStatusBasics(null));
        }
        
        SystemStatus oldSystemStatus = JSONObject.parseObject(productVO.getLatestSystemStatus(), SystemStatus.class);
        if (oldSystemStatus.getDate() == null) {
            return DataWithLinks.getDataWithLinks(sn, null, getSystemStatusBasics(null));
        }

        return getSystemStatus(sn, oldSystemStatus.getDate().toEpochSecond(), productVO.getLatestSystemStatus(), isRound);
    }

    public DataWithLinks<InverterInfo> getSystemStatus(String sn, long epoch, String defaultSystemStatus, boolean isRound) {
        DeviceVO lastDevice = deviceService.getLastDevice(sn, ApplicationName.Ross.name());
        String platformName = getValue(lastDevice, DeviceVO::getCloudPlatformName);
        CloudPlatformName cloudPlatformName = platformName != null && platformName.equals(CloudPlatformName.Tuya.name()) ? CloudPlatformName.Tuya : CloudPlatformName.Azure;
        TelemetryDataVO telemetryData = tableStoreServiceFactory.getService(cloudPlatformName).getTelemetryData(sn, epoch);
        String lastSystemStatus = getValue(telemetryData, TelemetryDataVO::getSystemStatus);
        if (lastSystemStatus == null) {
            if (cloudPlatformName == CloudPlatformName.Azure) {
                //数据迁移后，Azure取不到，Tuya再取一次
                telemetryData = tableStoreServiceFactory.getService(CloudPlatformName.Tuya).getTelemetryData(sn, epoch);
            }

            lastSystemStatus = getOrElse(telemetryData, TelemetryDataVO::getSystemStatus, defaultSystemStatus);
            if (lastSystemStatus == null) {
                return DataWithLinks.getDataWithLinks(sn, null, getSystemStatusBasics(null));
            }
        }

        try {
            SystemStatus systemStatus;
            if (isRound) {
                //小数位统一处理
                ParserConfig parserConfig = new ParserConfig();
                parserConfig.putDeserializer(BigDecimal.class, new CustomBigDecimalDeserializer());
                parserConfig.putDeserializer(Double.class, new CustomDoubleDeserializer());
                parserConfig.putDeserializer(double.class, new CustomDoubleDeserializer());

                systemStatus = JSONObject.parseObject(lastSystemStatus, SystemStatus.class, parserConfig);
            } else {
                systemStatus = JSONObject.parseObject(lastSystemStatus, SystemStatus.class);
            }

            AtAGlance<InverterInfo> atAGlance = getSystemStatusBasics(systemStatus);
            DataWithLinks<InverterInfo> dataWithLinks = DataWithLinks.getDataWithLinks(sn, systemStatus, atAGlance);
            return dataWithLinks;
        } catch (Exception e) {
            log.error("getLastStatus error, SN:{}", sn, e);
            return DataWithLinks.getDataWithLinks(sn, null, getSystemStatusBasics(null));
        }
    }

    public DataWithLinks<InverterInfo> getLastTelemetry(String sn) {
        ProductVO productVO = productService.getProduct(sn);
        if (productVO == null || StringUtils.isBlank(productVO.getLatestSystemStatus())) {
            return DataWithLinks.getDataWithLinks(sn, null, getTelemetryBasics(null));
        }

        SystemStatus oldSystemStatus = JSONObject.parseObject(productVO.getLatestSystemStatus(), SystemStatus.class);
        if (oldSystemStatus.getDate() == null) {
            return DataWithLinks.getDataWithLinks(sn, null, getTelemetryBasics(null));
        }

        return getTelemetryWithSummary(sn, oldSystemStatus.getDate().toEpochSecond());
    }

    public RossTelemetry getTelemetry(String sn, long epoch) {
        DeviceVO lastDevice = deviceService.getLastDevice(sn, ApplicationName.Ross.name());
        String platformName = getValue(lastDevice, DeviceVO::getCloudPlatformName);
        CloudPlatformName cloudPlatformName = platformName != null && platformName.equals(CloudPlatformName.Tuya.name()) ? CloudPlatformName.Tuya : CloudPlatformName.Azure;
        return tableStoreServiceFactory.getTelemetry(sn, epoch, cloudPlatformName);
    }

    public DataWithLinks<InverterInfo> getTelemetryWithSummary(String sn, long epoch) {
        RossTelemetry rossTelemetry = getTelemetry(sn, epoch);
        if (rossTelemetry == null) {
            return DataWithLinks.getDataWithLinks(sn, null, getTelemetryBasics(null));
        }

        try {
            AtAGlance<InverterInfo> atAGlance = getTelemetryBasics(rossTelemetry);
            DataWithLinks<InverterInfo> dataWithLinks = DataWithLinks.getDataWithLinks(sn, rossTelemetry, atAGlance);
            return dataWithLinks;
        } catch (Exception e) {
            log.error("getLastTelemetry error, SN:{}", sn, e);
            return DataWithLinks.getDataWithLinks(sn, null, getTelemetryBasics(null));
        }
    }

    public DataWithPermalinkVO<EnergyFlowExtendedVO> getLastEnergyflow(String sn, Long epoch) {
        ProductVO productVO = productService.getProduct(sn);
        if (productVO == null) {
            throw new BizException("The serial number doesn't exist.");
        }
        
        SystemStatus systemStatus;
        if (epoch == null) {
            if (StringUtils.isBlank(productVO.getLatestSystemStatus())) {
                return new DataWithPermalinkVO<>(null, null);
            }

            systemStatus = JSONObject.parseObject(productVO.getLatestSystemStatus(), SystemStatus.class);
        } else {
            DataWithLinks<InverterInfo> statusData = getSystemStatus(sn, epoch, null, false);
            systemStatus = (SystemStatus) statusData.getData();
        }
        
        ModelInfoDO modelInfoDO = productService.getModelInfo(sn);
        if (modelInfoDO == null) {
            throw new BizException("Model info not found for " + sn);
        }

        HardwareModelEnum hardwareModel = hardwareModelService.getHardwareModel(sn, modelInfoDO);
        HardwareCapabilityVO specification = hardwareModelService.getHardwareFirmwareSpecification(hardwareModel, 0);
        Boolean supportsConnectedPV = specification.isSupportsConnectedPV();

        boolean hasBatteries;
        boolean isRoss1 = getOrElse(systemStatus, SystemStatus::getOuijaBoard, OuijaBoard::getSoftwareVersion, VersionParser::isRoss1, true);
        if (isRoss1) {
            ConfigurationsVO configurationVO = configurationService.getByConfigType(sn, ConfigurationType.Electrical);
            if (configurationVO == null || StringUtils.isBlank(configurationVO.getConfigurations())) {
                throw new BizException("electrical config not found for " + sn);
            }

            ElectricalConfigurationVO electricalCfgVO = JSONObject.parseObject(configurationVO.getConfigurations(), ElectricalConfigurationVO.class);
            electricalCfgVO.patchLegacyBatteryInfo();
            hasBatteries = electricalCfgVO != null && electricalCfgVO.getBatteryType() != null
                    && !electricalCfgVO.getBatteryType().equals(BattType.None);
        } else {
            int major = VersionParser.getVersionMajor(systemStatus != null && systemStatus.getOuijaBoard() != null && systemStatus.getOuijaBoard().getSoftwareVersion() != null ?
                    systemStatus.getOuijaBoard().getSoftwareVersion() : null);
            ProductDeviceSettingsDO productDeviceSettings = deviceSettingsService.getProductDeviceSettings(sn);
            DesiredAndReportedVO desiredAndReported = productDeviceSettings == null ? DesiredAndReportedVO.getDefault() : new DesiredAndReportedVO(
                    JSONObject.parseObject(productDeviceSettings.getReportedDeviceSettings(), RossReportedSettingsVO.class),
                    JSONObject.parseObject(productDeviceSettings.getDesiredDeviceSettings(), RossDesiredSettingsVO.class));

            V2SettingVO v2SettingVO = null;
            String manufacturer = null;
            if (desiredAndReported.getReported() != null) {
                if (desiredAndReported.getReported().getProtocol() != null
                        && desiredAndReported.getReported().getProtocol().equals(SettingsProtocol.V2)) {
                    if (major == 2) {
                        manufacturer = getBatteryManufacturerFromDesired(desiredAndReported);
                    } else if (major == 4) {
                        HardwareModelEnum hardwareModelEnum = hardwareModelService.parseModelName(systemStatus != null && systemStatus.getInverter() != null ?
                                systemStatus.getInverter().getModelName() : null);
                        if (hardwareModelEnum != null) {
                            SettingsType settingsType = hardwareModelEnum.getSettingsTypeEnum();
                            if (settingsType.equals(SettingsType.None)) {
                                throw new BizException("No builder for model");
                            } else if (settingsType.equals(SettingsType.ESETSG)) {
                                manufacturer = getBatteryManufacturerFromDesired(desiredAndReported);
                            } else if (settingsType.equals(SettingsType.ESG)) {
                                v2SettingVO = new V2SettingVO(V2Section.BatteryManager, "Protocol");
                            } else if (settingsType.equals(SettingsType.EH1P)) {
                                v2SettingVO = new V2SettingVO(V2Section.BatteryManager, "Protocol");
                            } else if (settingsType.equals(SettingsType.HVH3P)) {
                                v2SettingVO = new V2SettingVO(V2Section.BatteryManager, "Protocol");
                            }
                        }
                    }
                } else {
                    manufacturer = getBatteryManufacturerFromDesired(desiredAndReported);
                }
            }

            manufacturer = StringUtils.isNoneBlank(manufacturer) ? manufacturer : toManufacturer(readString(desiredAndReported, v2SettingVO));
            hasBatteries = StringUtils.isNoneBlank(manufacturer) && !manufacturer.equals(ManufacturerEnum.None.name());
        }

        EnergyFlowExtendedVO extendedVO = EnergyFlowFactory.buildForViewModels(DataForDashboardVO.make(systemStatus), hasBatteries, supportsConnectedPV);
        if (extendedVO == null) {
            return new DataWithPermalinkVO<>(null, null);
        }

        return new DataWithPermalinkVO<>(extendedVO, null);
    }

    private String getBatteryManufacturerFromDesired(DesiredAndReportedVO desiredAndReported) {
        ManufacturerEnum manufacturer = getValue(desiredAndReported, DesiredAndReportedVO::getDesired, RossDesiredSettingsVO::getBatteryManager, BatteryManagerDesiredSettingsVO::getManufacturer);
        return manufacturer == null ? null : manufacturer.name();
    }

    private AtAGlance<InverterInfo> getTelemetryBasics(RossTelemetry t) {
        Instant instant = Instant.ofEpochSecond(0);
        ZoneId zoneId = ZoneId.of("UTC");
        ZonedDateTime zeroDateTime = ZonedDateTime.ofInstant(instant, zoneId);
        ZonedDateTime atUtc = t != null && t.getRossDeviceTelemetry() != null ? ZonedDateTime.ofInstant(t.getRossDeviceTelemetry().getTimestampUtc(), zoneId) : zeroDateTime;
        InverterInfo summary = InverterInfo.fromRossTelemetry(t != null ? t.getRossDeviceTelemetry() : null);

        Duration duration = Duration.between(atUtc.toInstant(), Instant.now());
        String howOld = TimeUtils.toHumanReadableString(TimeSpan.fromMilliseconds(Instant.now().toEpochMilli() - atUtc.toInstant().toEpochMilli()));

        return new AtAGlance<>(atUtc, summary, howOld);
    }

    public AtAGlance<InverterInfo> getSystemStatusBasics(SystemStatus t) {
        Instant instant = Instant.ofEpochSecond(0);
        ZoneId zoneId = ZoneId.of("UTC");
        ZonedDateTime zeroDateTime = ZonedDateTime.ofInstant(instant, zoneId);

        ZonedDateTime atUtc = (t != null && t.getDate() != null)
                ? t.getDate()
                : zeroDateTime; // 1970-01-01

        InverterInfo summary = inverterInfoFromSS(t);

        Map<Integer, ErrorMappingDO> allErrorMap = getAllErrorMap();
        if (t != null && !CollectionUtils.isEmpty(t.getSystemStatusErrors())) {
            t.getSystemStatusErrors().forEach(e -> {
                ErrorMappingDO errorMappingDO = allErrorMap.get(e.getErrorCode());
                if (errorMappingDO != null) {
                    e.setErrorDescription(errorMappingDO.getDescription());
                }
            });
        }

        String howOld = TimeUtils.toHumanReadableString(TimeSpan.fromMilliseconds(Instant.now().toEpochMilli() - atUtc.toInstant().toEpochMilli()));

        return new AtAGlance<>(atUtc, summary, howOld);
    }

    public static InverterInfo inverterInfoFromSS(SystemStatus t) {
        if (t == null) {
            return new InverterInfo(null, null, null, null, null);
        }

        String rossVersion = null;
        if (t.getOuijaBoard() != null && t.getOuijaBoard().getSoftwareVersion() != null) {
            rossVersion = t.getOuijaBoard().getSoftwareVersion()
                    .replace("ROSS v", "")
                    .replace(", ", ".");
        }

        boolean isRoss1 = t.getOuijaBoard() != null &&
                t.getOuijaBoard().getSoftwareVersion() != null &&
                t.getOuijaBoard().getSoftwareVersion().contains("ROSS v1");

        List<String> errors = InverterInfo.getErrors(
                t.getSystemStatusErrors() != null
                        ? t.getSystemStatusErrors().stream()
                        .map(e -> InverterInfo.fromSSError(isRoss1, e))
                        .collect(Collectors.toList())
                        : List.of()
        );

        return new InverterInfo(
                t.getInverter() != null ? t.getInverter().getModelName() : null,
                rossVersion,
                t.getInverter() != null ? t.getInverter().getFirmwareVersion() : null,
                t.getHardwareConfig(),
                errors
        );
    }

    public static Object ReadObject(DesiredAndReportedVO desiredAndReported, boolean isDesired, V2Section
            section, String key) {
        Map<String, Object> desired = null;
        switch (section) {
            case Inverter:
                if (desiredAndReported != null && desiredAndReported.getDesired() != null && desiredAndReported.getDesired().getV2() != null) {
                    desired = desiredAndReported.getDesired().getV2().getInverter();
                }
                break;
            case Meter:
                if (desiredAndReported != null && desiredAndReported.getDesired() != null && desiredAndReported.getDesired().getV2() != null) {
                    desired = desiredAndReported.getDesired().getV2().getMeter();
                }
                break;
            case BatteryManager:
                if (desiredAndReported != null && desiredAndReported.getDesired() != null && desiredAndReported.getDesired().getV2() != null) {
                    desired = desiredAndReported.getDesired().getV2().getBatteryManager();
                }
                break;
            case BatteryStack:
                desired = new HashMap<>();
                break;
            case InverterControl:
                if (desiredAndReported != null && desiredAndReported.getDesired() != null && desiredAndReported.getDesired().getV2() != null) {
                    desired = desiredAndReported.getDesired().getV2().getInverterControl();
                }
                break;
            case Site:
                if (desiredAndReported != null && desiredAndReported.getDesired() != null && desiredAndReported.getDesired().getV2() != null) {
                    desired = desiredAndReported.getDesired().getV2().getSite();
                }
                break;
            case Constraints:
                if (desiredAndReported != null && desiredAndReported.getDesired() != null && desiredAndReported.getDesired().getV2() != null) {
                    desired = desiredAndReported.getDesired().getV2().getConstraints();
                }
                break;
        }

        Map<String, Object> reported = null;
        switch (section) {
            case Inverter:
                if (desiredAndReported != null && desiredAndReported.getReported() != null && desiredAndReported.getReported().getV2() != null) {
                    reported = desiredAndReported.getReported().getV2().getInverter();
                }
                break;
            case Meter:
                if (desiredAndReported != null && desiredAndReported.getReported() != null && desiredAndReported.getReported().getV2() != null) {
                    reported = desiredAndReported.getReported().getV2().getMeter();
                }
                break;
            case BatteryManager:
                if (desiredAndReported != null && desiredAndReported.getReported() != null && desiredAndReported.getReported().getV2() != null) {
                    reported = desiredAndReported.getReported().getV2().getBatteryManager();
                }
                break;
            case BatteryStack:
                if (desiredAndReported != null && desiredAndReported.getReported() != null && desiredAndReported.getReported().getV2() != null) {
                    reported = desiredAndReported.getReported().getV2().getBatteryStack();
                }
                break;
            case InverterControl:
                reported = new HashMap<>();
                break;
            case Site:
                if (desiredAndReported != null && desiredAndReported.getReported() != null && desiredAndReported.getReported().getV2() != null) {
                    reported = desiredAndReported.getReported().getV2().getSite();
                }
                break;
            case Constraints:
                if (desiredAndReported != null && desiredAndReported.getReported() != null && desiredAndReported.getReported().getV2() != null) {
                    reported = desiredAndReported.getReported().getV2().getConstraints();
                }
                break;
        }

        if (isDesired && desired == null) {
            return null;
        }

        Map<String, Object> source = isDesired ? Collections.unmodifiableMap(desired) : reported;

        if (source == null) {
            return null;
        }

        return source.get(key);
    }

    public String readString(DesiredAndReportedVO desiredAndReported, V2SettingVO setting) {
        if (setting == null) {
            return null;
        }

        Object value = ReadObject(desiredAndReported, true, setting.getSection(), setting.getKey());
        return value != null ? value.toString() : null;
    }

    public static String toManufacturer(String protocol) {
        if (protocol == null || protocol.isEmpty()) {
            return "None";
        }

        if (protocol.toLowerCase().equals("none")) {
            return "None";
        }

        return "Pylon";
    }

    public Map<Integer, ErrorMappingDO> getAllErrorMap() {
        List<ErrorMappingDO> list = errorMappingMapper.selectList(new QueryWrapper<>());
        return list.stream().collect(Collectors.toMap(ErrorMappingDO::getId, Function.identity()));
    }
}
