package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ebon.energy.fms.domain.entity.DeviceDO;
import com.ebon.energy.fms.domain.vo.DeviceVO;
import com.ebon.energy.fms.mapper.third.DeviceMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DeviceService {

    @Resource
    private DeviceMapper deviceMapper;

    public DeviceVO getLastDevice(String serialNumber, String applicationName) {
        QueryWrapper<DeviceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("TOP 1 *");
        queryWrapper.eq("SerialNumber", serialNumber);
        queryWrapper.eq("ApplicationName", applicationName);
        queryWrapper.orderByDesc("ModifiedDateUtc");
        DeviceDO deviceDO = deviceMapper.selectOne(queryWrapper);

        if (deviceDO == null) {
            return null;
        }

        DeviceVO deviceVO = new DeviceVO();
        BeanUtils.copyProperties(deviceDO, deviceVO);

        return deviceVO;
    }

}
