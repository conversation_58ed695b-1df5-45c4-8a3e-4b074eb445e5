package com.ebon.energy.fms.service;

import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.alert.AlertDetails;
import com.ebon.energy.fms.domain.vo.alert.AlertModel;
import com.ebon.energy.fms.domain.vo.alert.SeverityCategoryModel;

/**
 * 告警业务服务接口
 * 处理告警相关的业务逻辑
 */
public interface AlertService {

    /**
     * 根据时间段获取告警汇总信息
     * 
     * @param timeRange 时间范围（分钟），为null时不进行时间过滤
     * @return 告警严重性分类统计模型
     */
    SeverityCategoryModel getAlertSummaryByPeriod(Integer timeRange);

    /**
     * 获取过滤后的告警数据（分页）
     * 
     * @param timeRange 时间范围（分钟）
     * @param sev 严重级别，逗号分隔
     * @param monitoringConditions 监控条件，逗号分隔
     * @param states 状态，逗号分隔
     * @param monitorConditionType 监控条件类型模糊查询
     * @param current 当前页码
     * @param pageSize 每页大小
     * @return 分页的告警数据
     */
    PageResult<AlertModel> getAlertData(
            int timeRange,
            String sev,
            String monitoringConditions,
            String states,
            String monitorConditionType,
            long current,
            long pageSize);

    /**
     * 根据ID获取告警详情
     * 
     * @param alertId 告警ID
     * @return 告警详情信息
     */
    AlertDetails getAlertDataById(String alertId);
}
