package com.ebon.energy.fms.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.alert.AlertDetails;
import com.ebon.energy.fms.domain.vo.alert.AlertModel;
import com.ebon.energy.fms.domain.vo.alert.SeverityCategoryModel;
import com.ebon.energy.fms.repository.AlertRepository;
import com.ebon.energy.fms.service.AlertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警业务服务实现类
 * 处理告警相关的业务逻辑
 */
@Slf4j
@Service
public class AlertServiceImpl implements AlertService {

    @Resource
    private AlertRepository alertRepository;

    @Override
    public SeverityCategoryModel getAlertSummaryByPeriod(Integer timeRange) {
        log.debug("开始获取告警汇总信息, timeRange: {}", timeRange);
        
        LocalDateTime dateRange = LocalDateTime.now().toLocalDate().atStartOfDay();
        
        // 获取所有告警数据
        List<AlertModel> alerts = alertRepository.getAllAlerts();
        
        // 如果指定了时间范围，则过滤数据
        if (timeRange != null) {
            dateRange = dateRange.minusMinutes(timeRange);
            final LocalDateTime finalDateRange = dateRange;
            alerts = alerts.stream()
                .filter(alert -> alert.getLastModifiedOnUtc().isAfter(finalDateRange))
                .collect(Collectors.toList());
        }
        
        // 构建严重性汇总数据
        SeverityCategoryModel severitySummary = new SeverityCategoryModel();
        
        // 计算各严重级别的总数
        severitySummary.setTotalSev1((int) alerts.stream().filter(x -> x.getSeverity() == 1).count());
        severitySummary.setTotalSev2((int) alerts.stream().filter(x -> x.getSeverity() == 2).count());
        severitySummary.setTotalSev3((int) alerts.stream().filter(x -> x.getSeverity() == 3).count());
        
        // 计算新告警数量
        severitySummary.setNewSev1((int) alerts.stream().filter(x -> x.getSeverity() == 1 && "New".equals(x.getState())).count());
        severitySummary.setNewSev2((int) alerts.stream().filter(x -> x.getSeverity() == 2 && "New".equals(x.getState())).count());
        severitySummary.setNewSev3((int) alerts.stream().filter(x -> x.getSeverity() == 3 && "New".equals(x.getState())).count());
        
        // 计算已关闭告警数量
        severitySummary.setClosedSev1((int) alerts.stream().filter(x -> x.getSeverity() == 1 && "Closed".equals(x.getState())).count());
        severitySummary.setClosedSev2((int) alerts.stream().filter(x -> x.getSeverity() == 2 && "Closed".equals(x.getState())).count());
        severitySummary.setClosedSev3((int) alerts.stream().filter(x -> x.getSeverity() == 3 && "Closed".equals(x.getState())).count());
        
        log.debug("告警汇总信息获取完成, 总告警数: {}", severitySummary.getTotalAlerts());
        return severitySummary;
    }

    @Override
    public PageResult<AlertModel> getAlertData(
            int timeRange,
            String sev,
            String monitoringConditions,
            String states,
            String monitorConditionType,
            long current,
            long pageSize) {
        
        log.debug("开始获取分页告警数据, timeRange: {}, current: {}, pageSize: {}", timeRange, current, pageSize);
        
        // 参数验证
        if (current < 1) {
            current = 1;
        }
        LocalDateTime dateRange = LocalDateTime.now().toLocalDate().atStartOfDay().minusMinutes(timeRange);

        // 解析过滤条件
        String[] monitoringConditionsArray = monitoringConditions.split(",");
        String[] severityArray = sev.split(",");
        String[] statesArray = states.split(",");

        // 创建分页对象
        Page<AlertModel> page = new Page<>(current, pageSize);

        // 获取分页的过滤后告警数据
        IPage<AlertModel> iPageResult = alertRepository.getFilteredAlertsWithPagination(
            page,
            dateRange,
            monitoringConditionsArray,
            severityArray,
            statesArray,
            monitorConditionType
        );

        // 转换为 PageResult
        PageResult<AlertModel> pageResult = new PageResult<>(
            (int) iPageResult.getCurrent(),
            (int) iPageResult.getSize(),
            iPageResult.getTotal(),
            iPageResult.getRecords()
        );

        log.debug("分页告警数据获取完成, 总记录数: {}, 当前页: {}", pageResult.getTotal(), pageResult.getCurrent());
        return pageResult;
    }

    @Override
    public AlertDetails getAlertDataById(String alertId) {
        log.debug("开始获取告警详情, alertId: {}", alertId);
        
        AlertDetails alertDetails = alertRepository.getAlertDetailsById(alertId);
        
        if (alertDetails == null || alertDetails.getDetail() == null) {
            log.warn("未找到指定的告警信息, alertId: {}", alertId);
            return null;
        }

        log.debug("告警详情获取完成, alertId: {}", alertId);
        return alertDetails;
    }
}
