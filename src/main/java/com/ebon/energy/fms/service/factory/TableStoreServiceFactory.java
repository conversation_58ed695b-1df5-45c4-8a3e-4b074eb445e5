package com.ebon.energy.fms.service.factory;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.domain.vo.TelemetryDataVO;
import com.ebon.energy.fms.domain.vo.telemetry.DataWithLinks;
import com.ebon.energy.fms.domain.vo.telemetry.RossTelemetry;
import com.ebon.energy.fms.domain.vo.telemetry.TelemetryExtensions;
import com.ebon.energy.fms.service.TableStoreService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

import static com.ebon.energy.fms.util.SafeAccess.getValue;

@Service
public class TableStoreServiceFactory {
    private final Map<CloudPlatformName, TableStoreService> services;

    @Autowired
    public TableStoreServiceFactory(List<TableStoreService> tableStoreServices) {
        services = new EnumMap<>(CloudPlatformName.class);
        tableStoreServices.forEach(service -> services.put(service.getCloudPlatformName(), service));
    }

    public TableStoreService getService(CloudPlatformName type) {
        TableStoreService service = services.get(type);
        if (service == null) {
            throw new IllegalArgumentException("No service found for type: " + type);
        }
        return service;
    }

    public RossTelemetry getTelemetry(String sn, long epoch, CloudPlatformName type) {
        TelemetryDataVO telemetryData = getService(type).getTelemetryData(sn, epoch);
        String telemetry = getValue(telemetryData, TelemetryDataVO::getTelemetry);
        if (telemetry == null) {
            if (type == CloudPlatformName.Azure) {
                //数据迁移后，Azure取不到，Tuya再取一次
                telemetryData = getService(CloudPlatformName.Tuya).getTelemetryData(sn, epoch);
            }

            telemetry = getValue(telemetryData, TelemetryDataVO::getTelemetry);
        }

        if (StringUtils.isNotBlank(telemetry)) {
            RossTelemetry rossTelemetry = JSONObject.parseObject(telemetry, RossTelemetry.class);
            TelemetryExtensions.rebuildDataBackedBands(rossTelemetry.getRossDeviceTelemetry());
            return rossTelemetry;
        }

        return null;
    }
}