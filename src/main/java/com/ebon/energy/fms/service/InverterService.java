package com.ebon.energy.fms.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.constants.InverterStatusConstants;
import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.SettingsReaderProvider;
import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.po.InvertersListPO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.product.control.ICommonSettingsReader;
import com.ebon.energy.fms.domain.vo.telemetry.*;
import com.ebon.energy.fms.domain.vo.telemetry.Error;
import com.ebon.energy.fms.mapper.second.InvertersMapper;
import com.ebon.energy.fms.mapper.second.InvertersTagsMapper;
import com.ebon.energy.fms.mapper.third.*;
import com.ebon.energy.fms.repository.impl.SettingsServiceImpl;
import com.ebon.energy.fms.util.InverterStatusHelper;
import com.ebon.energy.fms.util.StreamUtil;
import com.ebon.energy.fms.util.VersionParser;
import com.google.common.base.Strings;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.ebon.energy.fms.util.CalculationUtil.calculatePercentage;
import static com.ebon.energy.fms.util.SafeAccess.getOrElse;
import static com.ebon.energy.fms.util.SafeAccess.getValue;
import static com.ebon.energy.fms.util.StreamUtil.*;
import static com.ebon.energy.fms.util.StringHelper.cleanEscapeUnicode;
import static com.ebon.energy.fms.util.StringHelper.escapeForLike;
import static com.ebon.energy.fms.util.TimeUtils.getLocalDateStr;
import static com.ebon.energy.fms.util.TimeUtils.getUtcTimeStr;

@Slf4j
@Service
public class InverterService {

    @Resource
    private InvertersMapper invertersMapper;

    @Resource
    private InvertersTagsMapper invertersTagsMapper;

    @Resource
    private ProductDailyCachesMapper productDailyCachesMapper;

    @Resource
    private DeviceMapper deviceMapper;
    
    @Resource
    private ProductMapper productMapper;

    @Resource
    private ProductService productService;

    @Resource
    private AddressService addressService;

    @Resource
    private ForecastService forecastService;

    @Resource
    private SettingsService settingsService;

    @Resource
    private SettingsServiceImpl settingsServiceImpl;

    @Resource
    private ConfigurationService configurationService;

    @Resource
    private InverterAttentionService inverterAttentionService;

    @Resource
    private ModelVersionRelationService modelVersionRelationService;

    @Resource
    private UserDetailService userDetailService;
    
    @Resource
    private TelemetryService telemetryService;

    @Resource
    private AddressesMapper addressesMapper;

    @Resource
    private HardwareModelMapper hardwareModelMapper;

    @Resource
    private ProductInstallationMapper productInstallationMapper;

    public static final String regex_SNCode = "\"sNCode\":\"([^\"]*)\"";
    public static final String regex_Serial = "\"serial\":\"([^\"]*)\"";
    public static final String regex_BatteryModuleSn = "\"batteryModule\\d+SerialNumber\":\"([^\"]*)\"";

    public InverterInfoVO getInverterInfo(String sn) {
        InverterInfoVO vo = productService.getWithInstallation(sn);
        
        SystemStatus systemStatus = vo.getSystemStatus();
        InvertersDO invertersDO = getInverter(sn);
        vo.setMaintainingInstaller(invertersDO.getMaintainingInstaller());
        vo.setMaintainingInstallerEmail(invertersDO.getMaintainingInstallerEmail());
        vo.setMaintainingInstallerPhone(invertersDO.getMaintainingInstallerPhoneNumber());

        RedbackUserDetailsDO userDetail = userDetailService.getUserDetail(sn, UserPreferenceKey.ProductFriendlyName.name());
        if (userDetail != null) {
            vo.setInverterName(userDetail.getValue());
        }

        String inverterStatus = InverterStatusHelper.makeInverterStatus(invertersDO.getIsInWarranty(), invertersDO.getOffComms(), invertersDO.getAddress(), invertersDO.getSystemStatusTimeStampBrisbane() != null, false, false);
        vo.setInstallationDate(inverterStatus.equals(InverterStatusConstants.PendingInstall) ? null : (invertersDO.getActiveInstallationDate() != null ? DateFormatUtils.format(invertersDO.getActiveInstallationDate(), "dd/MM/yyyy") : null));
        vo.setPlantName(invertersDO.getProductOwner());

        if (StringUtils.isBlank(vo.getFirmwareVersion())) {
            vo.setFirmwareVersion(invertersDO.getInverterFirmware());
        }
        if (StringUtils.isBlank(vo.getModelName())) {
            vo.setModelName(invertersDO.getInverterModelName());
        }
        vo.setInverterModelName(vo.getModelName());
        if (StringUtils.isBlank(vo.getRossVersion())) {
            vo.setRossVersion(invertersDO.getRossVersion());
        }

        vo.setIsRoss1(VersionParser.isRoss1(vo.getRossVersion()));

        List<HardwareModelAndFamilyDO> hardwareModels = hardwareModelMapper.selectWithFamily();
        HardwareModelAndFamilyDO hardwareModelDO = hardwareModels.stream().filter(e -> e.getName().equalsIgnoreCase(vo.getModelName())).findFirst().orElse(null);
        vo.setModelName(hardwareModelDO != null ? hardwareModelDO.getDisplayName() : vo.getModelName());

        ProductWithOwnerDO productWithOwner = productService.getProductWithOwner(sn);
        vo.setSiteId(productWithOwner != null ? productWithOwner.getPublicSiteId() : null);
        vo.setAddress(getOrElse(productWithOwner, ProductWithOwnerDO::getAddressForUI, ""));
        vo.setOwnerContact(getOrElse(productWithOwner, ProductWithOwnerDO::getOwnerContact, ""));
        vo.setOwnerEmail(getOrElse(productWithOwner, ProductWithOwnerDO::getOwnerEmail, ""));
        vo.setOwnerFirstName(getOrElse(productWithOwner, ProductWithOwnerDO::getOwnerFirstName, ""));
        vo.setOwnerLastName(getOrElse(productWithOwner, ProductWithOwnerDO::getOwnerLastName, ""));
        vo.setOwnerName(productWithOwner != null ? (Strings.nullToEmpty(productWithOwner.getOwnerFirstName()) + " " + Strings.nullToEmpty(productWithOwner.getOwnerLastName())).trim() : "");
        vo.setNmi(getOrElse(productWithOwner, ProductWithOwnerDO::getNmi, ""));

        AddressesDO addressDO = addressesMapper.selectSiteAddressBySn(sn);
        LocalDate today = addressDO != null && StringUtils.isNoneBlank(addressDO.getTimeZoneId()) ?
                TimeZoneConverterUtil.getLocalDateFromBclTimeZone(Instant.now(), addressDO.getTimeZoneId())
                : LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        String dateStr = today.format(formatter);

        //年、月光伏发电量 (kWh) 统计
        int year = today.getYear();
        int month = today.getMonthValue();

        ProductDailyCachesDO totalDO = productDailyCachesMapper.selectTotal(sn);
        if (totalDO != null) {
            vo.setAllTimePvTotal(totalDO.getTotalGeneration() == null ? null : totalDO.getTotalGeneration().setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            vo.setAllTimeTotalImport(totalDO.getTotalImport() == null ? null : totalDO.getTotalImport().setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            vo.setAllTimeTotalExport(totalDO.getTotalExport() == null ? null : totalDO.getTotalExport().setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        }

        BigDecimal dayTotal = productDailyCachesMapper.selectDayTotalGeneration(sn, dateStr);
        BigDecimal monthTotal = productDailyCachesMapper.selectMonthTotalGeneration(sn, year, month);
        BigDecimal yearTotal = productDailyCachesMapper.selectYearTotalGeneration(sn, year);
        vo.setTodayPvTotal(dayTotal == null ? null : dayTotal.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        vo.setMonthPvTotal(monthTotal == null ? null : monthTotal.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        vo.setYearPvTotal(yearTotal == null ? null : yearTotal.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());

        InverterAttentionDO attentionDO = inverterAttentionService.getBySn(sn);
        vo.setErrorStatus(attentionDO != null ? attentionDO.getStatus() : 0);

        String batteryModel = getValue(systemStatus, SystemStatus::getBattery, BatteryStatus::getBatteryModel);
        vo.setBatteryModel(batteryModel != null && batteryModel.equalsIgnoreCase("US2KBPL") ? "US2000B+" : batteryModel);
        vo.setReportedBatteries(getOrElse(systemStatus, SystemStatus::getBattery, e -> e.getBatteries() == null ? 0 : e.getBatteries().size(), 0));
        vo.setDesiredBatteries(vo.getReportedBatteries());

        try {
            var settingsDto = settingsServiceImpl.getSettingsDtoAsync(sn);
            ICommonSettingsReader reader = SettingsReaderProvider.get(settingsDto);
            BatterySettingsDto batterySettings = reader.getBatterySettings(UniversalSettingSource.DESIRED);
            vo.setDesiredBatteries(getOrElse(batterySettings, BatterySettingsDto::getBatteryCount, MinMaxValueDto::getValue, 0));
        } catch (Exception e) {
        }

        if (systemStatus != null && systemStatus.getDate() != null) {
            RossTelemetry rossTelemetry = telemetryService.getTelemetry(sn, systemStatus.getDate().toEpochSecond());
            if (rossTelemetry != null) {
                String telemetryStr = JSONObject.toJSONString(rossTelemetry);
                vo.setBatterySerialNumbers(getBatterySns(telemetryStr));
            }
        }

        SupportNotesVO supportNotesVO = productMapper.selectSupportNotes(sn, ConfigurationType.DeviceControl.getValue(), UserType.InstallerCompany.getValue());
        vo.setSupportNotes(supportNotesVO);

        //InverterSettingsVO settingsVO = new InverterSettingsVO();
        
        Object settings = settingsService.getSettings(sn);
        if (settings instanceof DeviceSettingsVO) {
            DeviceSettingsVO deviceSettingsVO = (DeviceSettingsVO) settings;
            Map<String, Object> inverterMap = getValue(deviceSettingsVO, DeviceSettingsVO::getReported, RossReportedSettingsVO::getV2, SettingsV2ReportedVO::getInverter);
            if (inverterMap != null) {
                vo.setRatedPower(Objects.toString(inverterMap.get("RatedPower"), null));
                /*settingsVO.setForceChargePowerLimit(Objects.toString(inverterMap.get("ForceChargePowerLimit"), null));
                settingsVO.setBatteryAutoAwakenTimeSet(Objects.toString(inverterMap.get("BatteryAutoAwakenTimeSet"), null));
                settingsVO.setBatteryAwakenVolSet(Objects.toString(inverterMap.get("BatteryAwakenVolSet"), null));
                settingsVO.setBatteryAwakenTimeSet(Objects.toString(inverterMap.get("BatteryAwakenTimeSet"), null));*/
            }

            /*BatteryDesiredSettingsVO batteryDesiredSettingsVO = getValue(deviceSettingsVO, DeviceSettingsVO::getDesired, RossDesiredSettingsVO::getInverter, InverterDesiredSettingsVO::getBattery);
            if (batteryDesiredSettingsVO != null) {
                settingsVO.setMaxChargeCurrent(batteryDesiredSettingsVO.getMaxChargeCurrent());
                settingsVO.setMaxDischargeCurrent(batteryDesiredSettingsVO.getMaxDischargeCurrent());
                settingsVO.setMinSoCPercent(batteryDesiredSettingsVO.getMinStateOfChargePercent());
                settingsVO.setMinOffgridSoCPercent(batteryDesiredSettingsVO.getMinOffgridSoCPercent());
            }
            Map<String, Object> meterMap = getValue(deviceSettingsVO, DeviceSettingsVO::getReported, RossReportedSettingsVO::getV2, SettingsV2ReportedVO::getMeters);
            if (meterMap != null) {
                JSONObject grid = (JSONObject) meterMap.getOrDefault("grid", null);
                settingsVO.setMeterType(grid != null ? grid.getString("MeterTypeCode") : null);
            }*/
        }

        /*settingsVO.setBatteryModel(getValue(systemStatus, SystemStatus::getBattery, BatteryStatus::getBatteryModel
                , model -> model.equalsIgnoreCase("US2KBPL") ? "US2000B+" : model));

        ConfigurationsVO configurationsVO = configurationService.getByConfigType(sn, ConfigurationType.DeviceControl);
        DeviceControlVO deviceControlVO = JSONObject.parseObject(configurationsVO.getConfigurations(), DeviceControlVO.class);

        Optional<InverterOperationVO> inverterOperationVO = safeGet(deviceControlVO, DeviceControlVO::getInverterOperation);
        settingsVO.setAllowGridCharge(inverterOperationVO
                .map(InverterOperationVO::getType)
                .filter(t -> t.equalsIgnoreCase(InverterOperationModeType.Set.name())).orElse(null) != null);

        settingsVO.setPowerInWatts(inverterOperationVO
                .filter(e -> e.getMode().equalsIgnoreCase(InverterOperationMode.ImportPower.name()))
                .map(InverterOperationVO::getPowerInWatts)
                .orElse(null));

        List<InverterScheduleVO> inverterSchedules = inverterOperationVO
                .map(InverterOperationVO::getSchedules).orElse(null);
        InverterScheduleVO chargeSchedule = inverterSchedules.stream().filter(e -> e.getMode().equalsIgnoreCase(InverterOperationMode.ChargeBattery.name())).findFirst().orElse(null);
        if (chargeSchedule != null) {
            settingsVO.setChargeTime(chargeSchedule.getScheduleDateStr() + " " + chargeSchedule.getStartTime());
        }

        InverterScheduleVO dischargeSchedule = inverterSchedules.stream().filter(e -> e.getMode().equalsIgnoreCase(InverterOperationMode.DischargeBattery.name())).findFirst().orElse(null);
        if (dischargeSchedule != null) {
            settingsVO.setDischargeTime(dischargeSchedule.getScheduleDateStr() + " " + dischargeSchedule.getStartTime());
        }

        vo.setSettings(settingsVO);*/

        return vo;
    }

    public Long getInverterCount(InvertersListPO po) {
        return invertersMapper.countInverter(po, null, null);
    }

    public InvertersIndexVO getInverterList(InvertersListPO po) throws ExecutionException, InterruptedException {
        InvertersIndexVO indexVO = new InvertersIndexVO();
        Page<InvertersDO> page = new Page<>(po.getCurrent(), po.getPageSize());

        List<HardwareModelAndFamilyDO> hardwareModels = hardwareModelMapper.selectWithFamily();
        Map<String, HardwareModelAndFamilyDO> hardwareModelMap = hardwareModels.stream().collect(Collectors.toMap(HardwareModelAndFamilyDO::getName, Function.identity(), (first, second) -> first));

        final List<String> modelNames = new ArrayList<>();
        if (StringUtils.isNoneBlank(po.getInverterModelName())) {
            modelNames.addAll(StreamUtil.filterMapList(hardwareModels, e -> e.getDisplayName().contains(po.getInverterModelName()), HardwareModelAndFamilyDO::getName));
        }

        final List<String> serialNumbers = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(po.getTagIds())) {
            List<InverterTagsDO> invertersTags = getInvertersByTags(po.getTagIds());
            if (CollectionUtils.isNotEmpty(invertersTags)) {
                serialNumbers.addAll(invertersTags.stream().map(InverterTagsDO::getSerialNumber).collect(Collectors.toList()));
            }
        }

        String startTime = po.getErrorStartTime() != null && po.getErrorStartTime() > 0 ? getUtcTimeStr(po.getErrorStartTime()) : null;
        String endTime = po.getErrorEndTime() != null && po.getErrorEndTime() > 0 ? getUtcTimeStr(po.getErrorEndTime()) : null;
        po.setErrorStartTimeStr(startTime);
        po.setErrorEndTimeStr(endTime);
        po.setErrorKeyword(escapeForLike(po.getErrorKeyword()));

        List<CompletableFuture<Object>> futures = new ArrayList<>();
        CompletableFuture<Object> future = CompletableFuture
                .supplyAsync(() -> {
                    return invertersMapper.countInverter(po, serialNumbers, modelNames);
                });
        futures.add(future);

        CompletableFuture<Object> pageFuture = CompletableFuture
                .supplyAsync(() -> {
                    return invertersMapper.selectInverterPage((page.getCurrent() - 1) * page.getSize(),page.getSize(), po, serialNumbers, modelNames);
                });
        futures.add(pageFuture);

        InvertersListPO dailyPO = new InvertersListPO();
        BeanUtils.copyProperties(po, dailyPO);
        dailyPO.setIsDailyOnline(true);
        Boolean isDailyOnline = po.getIsDailyOnline();
        Long dailyOnlineCnt = 0L;
        CompletableFuture<Object> dailyFuture = null;
        if (isDailyOnline == null || isDailyOnline) {
            // 计算每日在线百分比
            dailyFuture = CompletableFuture
                    .supplyAsync(() -> {
                        return invertersMapper.countInverter(dailyPO, serialNumbers, modelNames);
                    });

            futures.add(dailyFuture);
        }

        // 计算每小时在线百分比
        InvertersListPO hourlyPO = new InvertersListPO();
        BeanUtils.copyProperties(po, hourlyPO);
        hourlyPO.setIsHourlyOnline(true);
        Boolean isHourlyOnline = po.getIsHourlyOnline();
        Long hourlyOnlineCnt = 0L;
        CompletableFuture<Object> hourlyFuture = null;
        if (isHourlyOnline == null || isHourlyOnline) {
            hourlyFuture = CompletableFuture
                    .supplyAsync(() -> {
                        return invertersMapper.countInverter(hourlyPO, serialNumbers, modelNames);
                    });
            futures.add(hourlyFuture);
        }

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

        // 设置超时时间，避免无限等待
        try {
            allFutures.get(20, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("查询逆变器数量超时", e);
            throw new BizException("查询逆变器数量超时");
        }

        Long count = (Long) future.get();
        if (dailyFuture != null) {
            dailyOnlineCnt = (Long) dailyFuture.get();
        }

        if (hourlyFuture != null) {
            hourlyOnlineCnt = (Long) hourlyFuture.get();
        }

        String dailyOnlinePercentage = calculatePercentage(dailyOnlineCnt, count);
        indexVO.setDailyOnlinePercentage(dailyOnlinePercentage);

        String hourlyOnlinePercentage = calculatePercentage(hourlyOnlineCnt, count);
        indexVO.setHourlyOnlinePercentage(hourlyOnlinePercentage);

        List<InverterExtDO> inverterList = (List<InverterExtDO>) pageFuture.get();

        if (count == 0) {
            indexVO.setDailyOnlinePercentage("0");
            indexVO.setHourlyOnlinePercentage("0");
            indexVO.setInverters(PageResult.toResponse(Collections.EMPTY_LIST, count, po.getCurrent(), po.getPageSize()));
            return indexVO;
        }

        final Map<String, InverterAttentionDO> attentionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(inverterList)) {
            List<String> sns = mapList(inverterList, InverterExtDO::getSerialNumber);
            List<InverterAttentionDO> attentions = inverterAttentionService.getByIds(sns);
            attentionMap.putAll(toMap(attentions, InverterAttentionDO::getRedbackProductSn));
        }

        List<InvertersVO> list = inverterList.stream().map(e -> {
            InvertersVO vo = new InvertersVO();
            BeanUtils.copyProperties(e, vo);

            vo.setFirmwareVersion(Strings.nullToEmpty(e.getInverterFirmware()));
            vo.setDetectedBatteryModel(Strings.nullToEmpty(e.getDetectedBatteryModel()));
            vo.setIsDailyOnline(e.getIsDailyOnline() != null ? e.getIsDailyOnline() : false);
            vo.setIsHourlyOnline(e.getIsHourlyOnline() != null ? e.getIsHourlyOnline() : false);
            vo.setIsSCCMOnline(e.getIsSCCMOnline() != null ? e.getIsSCCMOnline() : false);
            vo.setRossVersion(Strings.nullToEmpty(e.getRossVersion()));
            vo.setWindowsVersion(Strings.nullToEmpty(e.getWindowsVersion()));
            vo.setWindowsVersionSS(Strings.nullToEmpty(e.getWindowsVersionSS()));
            vo.setWatchDogVersion(Strings.nullToEmpty(e.getWatchDogVersion()));
            vo.setBatteryStatus(SystemStatusEnum.BatteryStatusValue.getNameFromValue(e.getBatteryStatus()));

            String modelName = e.getInverterModelName();
            HardwareModelAndFamilyDO hardwareModelDO = modelName != null ? hardwareModelMap.get(modelName) : null;
            vo.setInverterModelName(Strings.nullToEmpty(hardwareModelDO != null ? hardwareModelDO.getDisplayName() : modelName));
            if (StringUtils.isNoneBlank(e.getInverterMode())) {
                if (StringUtils.isNumeric(e.getInverterMode())) {
                    SystemStatusEnum.InverterModeValue inverterMode = SystemStatusEnum.InverterModeValue.fromValue(Integer.parseInt(e.getInverterMode()));
                } else {
                    SystemStatusEnum.InverterModeValue inverterMode = SystemStatusEnum.InverterModeValue.fromName(e.getInverterMode());
                    vo.setInverterMode(Strings.nullToEmpty(inverterMode == null ? vo.getInverterMode() : inverterMode.getDisplayName()));
                }
            } else {
                vo.setInverterMode(Strings.nullToEmpty(vo.getInverterMode()));
            }
            vo.setInstallerCompany(Strings.nullToEmpty(e.getMaintainingInstaller()));

            InverterAttentionDO attentionDO = attentionMap.get(e.getSerialNumber());
            vo.setNeedAttention(attentionDO != null ? attentionDO.getNeedAttention() : false);
            vo.setErrorStatus(attentionDO != null ? attentionDO.getStatus() : 0);
            if (attentionDO != null && StringUtils.isNotBlank(attentionDO.getLastErrors())) {
                List<Error> dbErrors = JSONObject.parseArray(attentionDO.getLastErrors(), Error.class);
                vo.setErrors(dbErrors);
            }

            return vo;
        }).collect(Collectors.toList());
        indexVO.setInverters(PageResult.toResponse(list, count, po.getCurrent(), po.getPageSize()));

        return indexVO;
    }

    private LambdaQueryWrapper getQueryWrapper(InvertersListPO po, List<HardwareModelAndFamilyDO> hardwareModels) {
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNoneBlank(po.getSerialNumber())) {
            queryWrapper.like(InvertersDO::getSerialNumber, po.getSerialNumber());
        }
        if (StringUtils.isNoneBlank(po.getRossVersion())) {
            queryWrapper.like(InvertersDO::getRossVersion, po.getRossVersion());
        }
        if (StringUtils.isNoneBlank(po.getFirmwareVersion())) {
            queryWrapper.like(InvertersDO::getInverterFirmware, po.getFirmwareVersion());
        }
        if (StringUtils.isNoneBlank(po.getInverterModelName())) {
            List<String> modelNames = StreamUtil.filterMapList(hardwareModels, e -> e.getDisplayName().contains(po.getInverterModelName()), HardwareModelAndFamilyDO::getName);
            queryWrapper.and(w -> w.in(CollectionUtils.isNotEmpty(modelNames), InvertersDO::getInverterModelName, modelNames) // IN条件（集合不为空时生效）
                    .or()
                    .like(InvertersDO::getInverterModelName, po.getInverterModelName()));
        }
        if (StringUtils.isNoneBlank(po.getInverterMode())) {
            queryWrapper.like(InvertersDO::getInverterMode, po.getInverterMode().replaceAll(" ", ""));
        }
        if (StringUtils.isNoneBlank(po.getInstallerCompany())) {
            queryWrapper.like(InvertersDO::getMaintainingInstaller, po.getInstallerCompany());
        }
        if (po.getIsDailyOnline() != null) {
            queryWrapper.eq(InvertersDO::getIsDailyOnline, po.getIsDailyOnline());
        }
        if (po.getIsHourlyOnline() != null) {
            queryWrapper.eq(InvertersDO::getIsHourlyOnline, po.getIsHourlyOnline());
        }
        if (po.getHasError() != null && po.getHasError()) {
            queryWrapper.apply("EXISTS (SELECT ia.RedbackProductSn FROM fms.dbo.InverterAttention ia where ia.RedbackProductSn=Inverters.SerialNumber and HasError=1)");
        }
        if (po.getNeedAttention() != null && po.getNeedAttention()) {
            queryWrapper.apply("EXISTS (SELECT ia.RedbackProductSn FROM fms.dbo.InverterAttention ia where ia.RedbackProductSn=Inverters.SerialNumber and NeedAttention=1)");
        }
        if (CollectionUtils.isNotEmpty(po.getTagIds())) {
            List<InverterTagsDO> invertersTags = getInvertersByTags(po.getTagIds());
            if (CollectionUtils.isNotEmpty(invertersTags)) {
                List<String> serialNumbers = invertersTags.stream().map(InverterTagsDO::getSerialNumber).collect(Collectors.toList());
                queryWrapper.in(InvertersDO::getSerialNumber, serialNumbers);
            }
        }
        if (po.getHasBattery() != null) {
            if (po.getHasBattery()) {
                //有电池
                queryWrapper.and(wrapper -> wrapper.isNull(InvertersDO::getInverterModelName)
                        .or().notLike(InvertersDO::getInverterModelName, "SI"));
                queryWrapper.isNotNull(InvertersDO::getDetectedBatteryManufacturer);
                queryWrapper.ne(InvertersDO::getDetectedBatteryManufacturer, ManufacturerEnum.None.name());
            } else {
                //无电池
                queryWrapper.and(wrapper -> wrapper.like(InvertersDO::getInverterModelName, "SI")
                        .or().isNull(InvertersDO::getDetectedBatteryManufacturer)
                        .or().eq(InvertersDO::getDetectedBatteryManufacturer, ManufacturerEnum.None.name()));
            }
        }

        return queryWrapper;
    }

    public Long countOnline(Boolean isDailyOnline, Boolean isHourlyOnline) {
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(isDailyOnline != null, InvertersDO::getIsDailyOnline, true);
        queryWrapper.eq(isHourlyOnline != null, InvertersDO::getIsHourlyOnline, true);
        return invertersMapper.selectCount(queryWrapper);
    }

    public List<InverterTagsDO> getInvertersByTags(List<Integer> tagIds) {
        LambdaQueryWrapper<InverterTagsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InverterTagsDO::getTagId, tagIds);
        return invertersTagsMapper.selectList(queryWrapper);
    }

    public List<InvertersDO> getInverterByQuery(String query) {
        List<InvertersDO> invertersDOS = invertersMapper.selectInvertersByQuery(query);
        return invertersDOS;
    }

    public InvertersDO getInverter(String sn) {
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InvertersDO::getSerialNumber, sn);
        return invertersMapper.selectOne(queryWrapper);
    }

    public PageResult<String> getSnPageByVersion(ModelTypeEnum type, String version, int current, int pageSize) {
        Page<InvertersDO> inverterPage = getPageByVersion(type, version, current, pageSize);
        return PageResult.toResponse(mapList(inverterPage.getRecords(), InvertersDO::getSerialNumber), inverterPage.getTotal(), current, pageSize);
    }

    public Page<InvertersDO> getPageByVersion(ModelTypeEnum type, String version, int current, int pageSize) {
        Page<InvertersDO> page = new Page<>(current, pageSize);
        List<FmsModelVersionRelationDO> versionRelations = modelVersionRelationService.listByVersion(type, version);
        List<Integer> modelIds = mapList(versionRelations, FmsModelVersionRelationDO::getModelId);
        if (CollectionUtils.isEmpty(modelIds)) {
            return page;
        }

        List<HardwareModelDO> hardwareModels = hardwareModelMapper.selectByIds(modelIds);
        List<String> modelNames = mapList(hardwareModels, HardwareModelDO::getName);
        return getPageByModelNames(modelNames, current, pageSize);
    }

    public Page<InvertersDO> getPageByModelNames(List<String> modelNames, int current, int pageSize) {
        Page<InvertersDO> page = new Page<>(current, pageSize);
        if (CollectionUtils.isEmpty(modelNames)) {
            return page;
        }

        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InvertersDO::getInverterModelName, modelNames);
        return invertersMapper.selectPage(page, queryWrapper);
    }

    public InverterForecastVO getInverterForecast(String sn, Integer frontDays, Integer afterDays) {
        // 计算日期范围
        AddressesDO addressDO = addressesMapper.selectSiteAddressBySn(sn);
        LocalDate now = addressDO != null && StringUtils.isNoneBlank(addressDO.getTimeZoneId()) ?
                TimeZoneConverterUtil.getLocalDateFromBclTimeZone(Instant.now(), addressDO.getTimeZoneId())
                : LocalDate.now();

        List<DailyTotalVO> nDayTotals = productService.getNDayTotals(sn, now, frontDays + 1);
        Map<String, DailyTotalVO> dayTotalMap = toMap(nDayTotals, DailyTotalVO::getDate);

        LocalDate startDate = now.minusDays(frontDays);
        LocalDate endDate = now.plusDays(afterDays);
        String startDateStr = getLocalDateStr(startDate);
        String endDateStr = getLocalDateStr(endDate);
        List<DayForecastVO> forecastList = forecastService.getDayForecastList(sn, startDateStr, endDateStr);
        Map<String, DayForecastVO> forecastMap = toMap(forecastList, DayForecastVO::getDate);

        // 创建完整的日期范围
        LocalDate today = now;
        List<DayForecastVO> dailyForecastList = IntStream.range(-frontDays, afterDays + 1)
                .mapToObj(i -> today.plusDays(i))
                .map(date -> DayForecastVO.empty(date))
                .sorted(Comparator.comparing(DayForecastVO::getDate))
                .collect(Collectors.toList());

        for (DayForecastVO forecastVO : dailyForecastList) {
            DayForecastVO fcVO = forecastMap.get(forecastVO.getDate());
            if (fcVO != null) {
                forecastVO.setForecastWh(fcVO.getForecastWh());
                forecastVO.setForecastKwh(fcVO.getForecastKwh());
            }

            DailyTotalVO dailyTotalVO = dayTotalMap.get(forecastVO.getDate());
            if (dailyTotalVO != null && !dailyTotalVO.getDailyGenerationAdjusted()) {
                forecastVO.setWh(new BigDecimal(dailyTotalVO.getDailyGenerationWh()));
                forecastVO.setKwh(WattHour.getKwh(forecastVO.getWh()));
            }
        }

        InverterForecastVO res = new InverterForecastVO();
        res.setList(dailyForecastList);
        return res;
    }

    public void updateBatch(List<InvertersDO> inverters) {
        final int batchSize = 400;
        List<List<InvertersDO>> batches = new ArrayList<>();
        for (int i = 0; i < inverters.size(); i += batchSize) {
            batches.add(inverters.subList(i, Math.min(i + batchSize, inverters.size())));
        }

        for (List<InvertersDO> batch : batches) {
            invertersMapper.updateBatch(batch);
        }

        /*List<CompletableFuture<Void>> futures = batches.stream()
                .map(batch -> CompletableFuture.runAsync(() -> {
                    log.info("inverter update batch:{}", batch);
                    invertersMapper.updateBatch(batch);
                }))
                .collect(Collectors.toList());

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));*/
    }


    @SneakyThrows
    public void exportExcel(InvertersListPO invertersListPO, HttpServletResponse httpServletResponse) {
        try {
            // 设置不分页，获取所有数据
            invertersListPO.setCurrent(1);
            invertersListPO.setPageSize(Integer.MAX_VALUE);

            // 获取逆变器列表数据
            InvertersIndexVO inverterList = getInverterList(invertersListPO);
            List<InvertersExcelVO> excelVoList = inverterList.getInverters().getList()
                    .stream()
                    .map(InvertersExcelVO::convert)
                    .collect(Collectors.toList());

            // 设置响应头
            String fileName = "invert_attention_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

            httpServletResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpServletResponse.setCharacterEncoding("utf-8");
            httpServletResponse.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
            httpServletResponse.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition");

            // 使用EasyExcel写入数据
            EasyExcel.write(httpServletResponse.getOutputStream(), InvertersExcelVO.class)
                    .sheet("invert")
                    .doWrite(excelVoList);

        } catch (IOException e) {
            log.error("导出Excel失败", e);
            throw new RuntimeException("export failed", e);
        }
    }

    private List<String> getBatterySns(String telemetry) {
        Pattern pattern1 = Pattern.compile(regex_SNCode);
        Matcher matcher1 = pattern1.matcher(telemetry);
        List<String> batterySns = new ArrayList<>();
        while (matcher1.find()) {
            //提取捕获组中的值
            String batteryTypeValue = matcher1.group(1);
            if (StringUtils.isNotBlank(batteryTypeValue)) {
                batterySns.add(cleanEscapeUnicode(batteryTypeValue));
            }
        }

        Pattern pattern2 = Pattern.compile(regex_Serial);
        Matcher matcher2 = pattern2.matcher(telemetry);
        while (matcher2.find()) {
            //提取捕获组中的值
            String batteryTypeValue = matcher2.group(1);
            if (StringUtils.isNotBlank(batteryTypeValue)) {
                batterySns.add(cleanEscapeUnicode(batteryTypeValue));
            }
        }

        Pattern pattern3 = Pattern.compile(regex_BatteryModuleSn);
        Matcher matcher3 = pattern3.matcher(telemetry);
        while (matcher3.find()) {
            //提取捕获组中的值
            String batteryTypeValue = matcher3.group(1);
            if (StringUtils.isNotBlank(batteryTypeValue)) {
                batterySns.add(cleanEscapeUnicode(batteryTypeValue));
            }
        }

        return filterList(batterySns, e -> StringUtils.isNotBlank(e));
    }

}
