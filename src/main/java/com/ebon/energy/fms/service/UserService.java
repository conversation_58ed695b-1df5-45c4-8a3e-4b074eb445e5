package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.common.constants.CacheConstant;
import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.TokenGenerateUtil;
import com.ebon.energy.fms.config.EmailConfig;
import com.ebon.energy.fms.domain.entity.CacheDataDO;
import com.ebon.energy.fms.domain.entity.FmsRoleDO;
import com.ebon.energy.fms.domain.entity.FmsUserDO;
import com.ebon.energy.fms.domain.entity.FmsUserRoleDO;
import com.ebon.energy.fms.domain.po.*;
import com.ebon.energy.fms.domain.vo.MailMessage;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.UserListVO;
import com.ebon.energy.fms.domain.vo.UserVO;
import com.ebon.energy.fms.mapper.fourth.AspNetUsersMapper;
import com.ebon.energy.fms.mapper.primary.CacheDataMapper;
import com.ebon.energy.fms.mapper.primary.RoleMapper;
import com.ebon.energy.fms.mapper.primary.UserMapper;
import com.ebon.energy.fms.mapper.primary.UserRoleMapper;
import com.ebon.energy.fms.util.CookieUtil;
import com.ebon.energy.fms.util.MD5Util;
import com.ebon.energy.fms.util.PasswordGenerator;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.ebon.energy.fms.common.constants.AuthorizeConstants.BASE_SALT;
import static com.ebon.energy.fms.common.constants.AuthorizeConstants.COOKIE_TOKEN_KEY;
import static com.ebon.energy.fms.common.constants.GlobalConstants.SUPER_ROLE_ID;
import static com.ebon.energy.fms.util.BlueimpMd5Util.getPasswordWithDefaultSalt;
import static com.ebon.energy.fms.util.StreamUtil.toMap;

@Slf4j
@Service
public class UserService {

    @Resource
    private JwtService jwtService;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserRoleMapper userRoleMapper;
    
    @Resource
    private AspNetUsersMapper aspNetUsersMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private FleetMonitoringUsersService fleetMonitoringUsersService;
    
    @Resource
    private EmailService emailService;

    @Resource
    private EmailConfig emailConfig;

    @Resource
    private CacheDataMapper cacheDataMapper;

    @Value("${findPasswordLink}")
    private String findPasswordLink;

    public void login(String email, String password, HttpServletResponse response) {
        FmsUserDO fmsUserDO = userMapper.selectByEmail(email);
        if (fmsUserDO == null) {
            throw new BizException(CommonErrorCodeEnum.USER_NOT_EXIST);
        }

        if (!checkPassword(email, password, fmsUserDO.getPassword())) {
            throw new BizException(CommonErrorCodeEnum.PASSWORD_ERROR);
        }

        if (!fmsUserDO.getStatus()) {
            throw new BizException(CommonErrorCodeEnum.USER_DISABLED);
        }

        String jwtToken = jwtService.generateToken(fmsUserDO.getEmail());
        CookieUtil.create(response, COOKIE_TOKEN_KEY, jwtToken, false);

        // 老系统生成此用户
        if (!fleetMonitoringUsersService.existInOldVersion(email)) {
            fleetMonitoringUsersService.addUserInOldVersion(email);
        }
        
        fmsUserDO.setLastLoginTime(new Timestamp(System.currentTimeMillis()));
        userMapper.updateById(fmsUserDO);
    }

    public void loginOut(HttpServletResponse response) {
        CookieUtil.clear(response, COOKIE_TOKEN_KEY);
    }

    private boolean checkPassword(String email, String inputPassword, String passwordInDb) {
        return getPasswordWithSalt(inputPassword, email).equals(passwordInDb);
    }

    public List<FmsUserDO> getUsers(List<Integer> ids) {
        LambdaQueryWrapper<FmsUserDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FmsUserDO::getId, ids);
        return userMapper.selectList(queryWrapper);
    }

    public Map<Integer, FmsUserDO> getUserMap(List<Integer> ids) {
        return toMap(getUsers(ids), FmsUserDO::getId);
    }

    public UserVO getUserByEmail(String email) {
        FmsUserDO userDO = userMapper.selectByEmail(email);
        if(userDO == null){
            return null;
        }

        return UserVO.builder().userId(userDO.getId()).email(userDO.getEmail())
                .name(userDO.getName()).status(userDO.getStatus())
                .portalUserId(userDO.getPortalUserId()).build();
    }

    private String getPasswordWithSalt(String password, String salt) {
        return MD5Util.getMD5(salt + password + BASE_SALT);
    }

    public PageResult<UserListVO> getUserList(UserListPO userListPO) {
        int offset = (userListPO.getCurrent() - 1) * userListPO.getPageSize();
        List<UserListVO> userListVOs = userMapper.selectUserList(userListPO.getUserName(), userListPO.getEmail(), userListPO.getRoleId(), offset, userListPO.getPageSize());
        long total = userMapper.countUserList(userListPO.getUserName(), userListPO.getEmail(), userListPO.getRoleId());
        return PageResult.toResponse(userListVOs, total, userListPO.getCurrent(), userListPO.getPageSize());
    }

    public void createUser(CreateUserPO createUserPO) {
        FmsUserDO userExist = userMapper.selectByEmail(createUserPO.getEmail());
        if (Objects.nonNull(userExist)) {
            throw new BizException(CommonErrorCodeEnum.USER_ALREADY_EXISTS);
        }
        FmsRoleDO roleExist = roleMapper.findByRoleId(createUserPO.getRoleId());
        if (Objects.isNull(roleExist)) {
            throw new BizException(CommonErrorCodeEnum.ROLE_NOT_EXIST);
        }
        if (!roleExist.isEnable() || roleExist.getId() == SUPER_ROLE_ID) {
            throw new BizException(CommonErrorCodeEnum.ROLE_UNAVAILABLE);
        }
        FmsUserDO user = new FmsUserDO();
        user.setName(createUserPO.getName());
        user.setEmail(createUserPO.getEmail());
        user.setStatus(createUserPO.getStatus());

        String password = PasswordGenerator.generatePassword();
        String passwordEncrypted = getPasswordWithDefaultSalt(password);
        String newPassword = getPasswordWithSalt(passwordEncrypted, createUserPO.getEmail());
        user.setPassword(newPassword);

        String portalUserId = aspNetUsersMapper.selectPortalUserId(createUserPO.getEmail());
        if (portalUserId != null) {
            user.setPortalUserId(portalUserId);
        }

        userMapper.insert(user);
        userRoleMapper.bindUserRole(user.getId(), createUserPO.getRoleId());

        // 老系统生成此用户
        if (!fleetMonitoringUsersService.existInOldVersion(createUserPO.getEmail())) {
            fleetMonitoringUsersService.addUserInOldVersion(createUserPO.getEmail());
        }

        sendPwdEmail(user, "Initial Password", password);
    }

    public void modifyUser(ModifyUserPO modifyUserPO) {
        FmsUserDO userExist = userMapper.selectById(modifyUserPO.getId());
        if (Objects.isNull(userExist)) {
            throw new BizException(CommonErrorCodeEnum.USER_NOT_EXIST);
        }
        FmsRoleDO roleExist = roleMapper.findByRoleId(modifyUserPO.getRoleId());
        if (Objects.isNull(roleExist)) {
            throw new BizException(CommonErrorCodeEnum.ROLE_NOT_EXIST);
        }
        FmsUserRoleDO fmsUserRoleDO = userRoleMapper.findByUserId(modifyUserPO.getId());
        if (!roleExist.isEnable()
                || ((fmsUserRoleDO == null || fmsUserRoleDO.getRoleId() != SUPER_ROLE_ID) && modifyUserPO.getRoleId() == SUPER_ROLE_ID)) {
            throw new BizException(CommonErrorCodeEnum.ROLE_UNAVAILABLE);
        }
        userMapper.modifyUser(modifyUserPO.getId(), modifyUserPO.getName(), modifyUserPO.getStatus());
        if (Objects.isNull(fmsUserRoleDO)) {
            userRoleMapper.bindUserRole(modifyUserPO.getId(), modifyUserPO.getRoleId());
        } else {
            userRoleMapper.modifyUserRole(modifyUserPO.getId(), modifyUserPO.getRoleId());
        }
    }

    public void updatePassword(UpdatePasswordPO updatePasswordPO) {
        String email = RequestUtil.getLoginUserEmail();
        FmsUserDO currentUser = userMapper.selectByEmail(email);
        if (!checkPassword(currentUser.getEmail(), updatePasswordPO.getOldPassword(), currentUser.getPassword())) {
            throw new BizException(CommonErrorCodeEnum.PASSWORD_ERROR);
        }
        String newPasswordMd5 = MD5Util.getMD5(currentUser.getEmail() + updatePasswordPO.getNewPassword() + BASE_SALT);
        currentUser.setPassword(newPasswordMd5);
        userMapper.updateById(currentUser);
    }

    public void resetPassword(ResetPasswordPO resetPasswordPO) {
        FmsUserDO userDO = userMapper.selectById(resetPasswordPO.getId());
        String password = PasswordGenerator.generatePassword();
        String passwordEncrypted = getPasswordWithDefaultSalt(password);
        String newPassword = getPasswordWithSalt(passwordEncrypted, userDO.getEmail());
        userDO.setPassword(newPassword);
        userMapper.updateById(userDO);

        sendPwdEmail(userDO, "Password Reset", password);
    }

    private void sendPwdEmail(FmsUserDO userDO,String subject, String password) {
        try {
            String name = StringUtils.isNoneBlank(userDO.getName()) ? userDO.getName() : userDO.getEmail().split("@")[0];
                    
            MailMessage msg = new MailMessage();
            msg.setFrom(emailConfig.getFromAddress());
            msg.setTo(List.of(userDO.getEmail()));
            msg.setSubject(subject);
            if (subject.equals("Initial Password")) {
                msg.setContent("hi " + name + ", <br>your user has been created, and the initial password is: " + password);
            } else {
                msg.setContent("hi " + name + ", <br>your password has been reset to: " + password);
            }
            emailService.sendEmail(msg, null);
        } catch (Exception e) {
            log.error("send pwd email error", e);
        }
    }

    public void findPasswordCheckEmail(String email) {
        FmsUserDO fmsUserDO = userMapper.selectByEmail(email);
        if (fmsUserDO == null) {
            throw new BizException(CommonErrorCodeEnum.USER_NOT_EXIST);
        }
        String token = TokenGenerateUtil.generateBase64Token();
        LocalDateTime expireDateTime = LocalDateTime.now().plusHours(CacheConstant.FIND_PASSWORD_EXPIRE);
        Timestamp expireAt = Timestamp.valueOf(expireDateTime);
        CacheDataDO cacheData = new CacheDataDO(String.format(CacheConstant.FIND_PASSWORD_KEY, token), email, expireAt);
        cacheDataMapper.save(cacheData);
        sendFindPwdEmail(fmsUserDO, "Find Password", token);
    }

    public void findPasswordModifyPassword(FindPasswordStepTwoPO findPasswordStepTwoPO) {
        CacheDataDO cacheData = cacheDataMapper.selectByKey(String.format(CacheConstant.FIND_PASSWORD_KEY, findPasswordStepTwoPO.getToken()));
        if (Objects.isNull(cacheData)) {
            throw new BizException(CommonErrorCodeEnum.PARAM_INVALID_ERROR);
        }
        if (!Objects.isNull(cacheData.getExpireAt()) && cacheData.getExpireAt().before(Timestamp.valueOf(LocalDateTime.now()))) {
            throw new BizException(CommonErrorCodeEnum.FIND_PASSWORD_LINK_HAS_EXPIRED);
        }
        FmsUserDO fmsUserDO = userMapper.selectByEmail(cacheData.getDataValue());
        if (Objects.isNull(fmsUserDO)) {
            throw new BizException(CommonErrorCodeEnum.USER_NOT_EXIST);
        }
        String newPasswordMd5 = MD5Util.getMD5(fmsUserDO.getEmail() + findPasswordStepTwoPO.getNewPassword() + BASE_SALT);
        fmsUserDO.setPassword(newPasswordMd5);
        userMapper.updateById(fmsUserDO);
        cacheDataMapper.deleteCache(String.format(CacheConstant.FIND_PASSWORD_KEY, findPasswordStepTwoPO.getToken()));
    }

    private void sendFindPwdEmail(FmsUserDO fmsUserDO, String subject, String token) {
        try {
            String name = StringUtils.isNoneBlank(fmsUserDO.getName()) ? fmsUserDO.getName() : fmsUserDO.getEmail().split("@")[0];

            MailMessage msg = new MailMessage();
            msg.setFrom(emailConfig.getFromAddress());
            msg.setTo(List.of(fmsUserDO.getEmail()));
            msg.setSubject(subject);
            msg.setContent("hi " + name + ", <br>Click the link below to reset your password: " + "<br><br>" + findPasswordLink + "?token=" + token);
            emailService.sendEmail(msg, null);
        } catch (Exception e) {
            log.error("send find pwd email error", e);
        }
    }
}