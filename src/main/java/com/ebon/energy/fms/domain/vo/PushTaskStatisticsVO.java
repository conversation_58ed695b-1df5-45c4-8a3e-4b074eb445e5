package com.ebon.energy.fms.domain.vo;

import lombok.Data;

/**
 * 推送任务统计响应对象
 */
@Data
public class PushTaskStatisticsVO {

    /**
     * 总任务数
     */
    private Long totalTasks;

    /**
     * 待发送任务数
     */
    private Long pendingTasks;

    /**
     * 已发送任务数
     */
    private Long sentTasks;

    /**
     * 发送失败任务数
     */
    private Long failedTasks;

    /**
     * 今日创建任务数
     */
    private Long todayCreatedTasks;

    /**
     * 今日发送成功任务数
     */
    private Long todaySentTasks;

    /**
     * 推送成功率（百分比）
     */
    private Double successRate;

    /**
     * 平均重试次数
     */
    private Double averageRetryCount;
}
