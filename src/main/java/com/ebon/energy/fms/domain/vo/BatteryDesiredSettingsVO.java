package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

@Data
public final class BatteryDesiredSettingsVO {

    @JsonProperty("maxChargeVoltage")
    private final BigDecimal maxChargeVoltage;

    @JsonProperty("minDischargeVoltage")
    private final BigDecimal minDischargeVoltage;

    @JsonProperty("maxChargeCurrent")
    private final Integer maxChargeCurrent;

    @JsonProperty("maxDischargeCurrent")
    private final Integer maxDischargeCurrent;

    @JsonProperty("maxOffgridDischargeCurrent")
    private final Integer maxOffgridDischargeCurrent;

    @JsonProperty("minStateOfChargePercent")
    private final Integer minStateOfChargePercent;

    @JsonProperty("MinOffgridSoCPercent")
    private final Integer minOffgridSoCPercent;

    @JsonProperty("ChargeVMaxOverride")
    private final BigDecimal chargeVMaxOverride;

    @JsonProperty("ChargeIMaxOverride")
    private final BigDecimal chargeIMaxOverride;

    @JsonProperty("DischargeVMinOverride")
    private final BigDecimal dischargeVMinOverride;

    @JsonProperty("DischargeIMaxOverride")
    private final BigDecimal dischargeIMaxOverride;

    @JsonProperty("SoC0to1Override")
    private final BigDecimal soc0to1Override;

    @JsonProperty("WarningCodeOverride")
    private final Short warningCodeOverride;

    @JsonProperty("AlarmCodeOverride")
    private final Short alarmCodeOverride;

    @JsonProperty("StatusOverride")
    private final Short statusOverride;

    @JsonProperty("HighSoCProtection0to1")
    private final BigDecimal highSoCProtection0to1;

    @JsonProperty("HighSoCProtectionIA")
    private final BigDecimal highSoCProtectionIA;

    @JsonProperty("IsLeadAcidSpecialOn")
    private final Boolean isLeadAcidSpecialOn;

    @JsonProperty("DisableLowVoltageChargeLimit")
    private final Boolean disableLowVoltageChargeLimit;

    @JsonProperty("LowVoltageChargeLimitThresholdV")
    private final BigDecimal lowVoltageChargeLimitThresholdV;

    @JsonProperty("LowVoltageChargeLimitMultiplierC")
    private final BigDecimal lowVoltageChargeLimitMultiplierC;

    @JsonProperty("MaxPerModuleChargeVoltage")
    private final BigDecimal maxPerModuleChargeVoltage;

    public BatteryDesiredSettingsVO(BatteryDesiredSettingsVO b) {
        this(
                b.maxChargeVoltage, b.minDischargeVoltage,
                b.maxChargeCurrent, b.maxDischargeCurrent, b.maxOffgridDischargeCurrent,
                b.minStateOfChargePercent, b.minOffgridSoCPercent,
                b.chargeVMaxOverride, b.chargeIMaxOverride,
                b.dischargeVMinOverride, b.dischargeIMaxOverride,
                b.soc0to1Override, b.warningCodeOverride, b.alarmCodeOverride,
                b.statusOverride, b.highSoCProtection0to1, b.highSoCProtectionIA,
                b.isLeadAcidSpecialOn, b.disableLowVoltageChargeLimit,
                b.lowVoltageChargeLimitThresholdV, b.lowVoltageChargeLimitMultiplierC,
                b.maxPerModuleChargeVoltage
        );
    }

    public BatteryDesiredSettingsVO(
            BigDecimal maxChargeVoltage, BigDecimal minDischargeVoltage,
            Integer maxChargeCurrent, Integer maxDischargeCurrent, Integer maxOffgridDischargeCurrent,
            Integer minStateOfChargePercent, Integer minOffgridSoCPercent,
            BigDecimal chargeVMaxOverride, BigDecimal chargeIMaxOverride,
            BigDecimal dischargeVMinOverride, BigDecimal dischargeIMaxOverride,
            BigDecimal soc0to1Override, Short warningCodeOverride, Short alarmCodeOverride,
            Short statusOverride, BigDecimal highSoCProtection0to1, BigDecimal highSoCProtectionIA,
            Boolean isLeadAcidSpecialOn, Boolean disableLowVoltageChargeLimit,
            BigDecimal lowVoltageChargeLimitThresholdV, BigDecimal lowVoltageChargeLimitMultiplierC,
            BigDecimal maxPerModuleChargeVoltage) {

        this.maxChargeVoltage = maxChargeVoltage;
        this.minDischargeVoltage = minDischargeVoltage;
        this.maxChargeCurrent = maxChargeCurrent;
        this.maxDischargeCurrent = maxDischargeCurrent;
        this.maxOffgridDischargeCurrent = maxOffgridDischargeCurrent;
        this.minStateOfChargePercent = minStateOfChargePercent;
        this.minOffgridSoCPercent = minOffgridSoCPercent;
        this.chargeVMaxOverride = chargeVMaxOverride;
        this.chargeIMaxOverride = chargeIMaxOverride;
        this.dischargeVMinOverride = dischargeVMinOverride;
        this.dischargeIMaxOverride = dischargeIMaxOverride;
        this.soc0to1Override = soc0to1Override;
        this.warningCodeOverride = warningCodeOverride;
        this.alarmCodeOverride = alarmCodeOverride;
        this.statusOverride = statusOverride;
        this.highSoCProtection0to1 = highSoCProtection0to1;
        this.highSoCProtectionIA = highSoCProtectionIA;
        this.isLeadAcidSpecialOn = isLeadAcidSpecialOn;
        this.disableLowVoltageChargeLimit = disableLowVoltageChargeLimit;
        this.lowVoltageChargeLimitThresholdV = lowVoltageChargeLimitThresholdV;
        this.lowVoltageChargeLimitMultiplierC = lowVoltageChargeLimitMultiplierC;
        this.maxPerModuleChargeVoltage = maxPerModuleChargeVoltage;
    }

    public static BatteryDesiredSettingsVO getDefault() {
        return new BatteryDesiredSettingsVO(null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, null,
                null, null, null, null, null, null, null
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BatteryDesiredSettingsVO that = (BatteryDesiredSettingsVO) o;
        return Objects.equals(maxChargeVoltage, that.maxChargeVoltage) &&
                Objects.equals(minDischargeVoltage, that.minDischargeVoltage) &&
                Objects.equals(maxChargeCurrent, that.maxChargeCurrent) &&
                Objects.equals(maxDischargeCurrent, that.maxDischargeCurrent) &&
                Objects.equals(maxOffgridDischargeCurrent, that.maxOffgridDischargeCurrent) &&
                Objects.equals(minStateOfChargePercent, that.minStateOfChargePercent) &&
                Objects.equals(minOffgridSoCPercent, that.minOffgridSoCPercent) &&
                Objects.equals(chargeVMaxOverride, that.chargeVMaxOverride) &&
                Objects.equals(chargeIMaxOverride, that.chargeIMaxOverride) &&
                Objects.equals(dischargeVMinOverride, that.dischargeVMinOverride) &&
                Objects.equals(dischargeIMaxOverride, that.dischargeIMaxOverride) &&
                Objects.equals(soc0to1Override, that.soc0to1Override) &&
                Objects.equals(warningCodeOverride, that.warningCodeOverride) &&
                Objects.equals(alarmCodeOverride, that.alarmCodeOverride) &&
                Objects.equals(statusOverride, that.statusOverride) &&
                Objects.equals(highSoCProtection0to1, that.highSoCProtection0to1) &&
                Objects.equals(highSoCProtectionIA, that.highSoCProtectionIA) &&
                Objects.equals(isLeadAcidSpecialOn, that.isLeadAcidSpecialOn) &&
                Objects.equals(disableLowVoltageChargeLimit, that.disableLowVoltageChargeLimit) &&
                Objects.equals(lowVoltageChargeLimitThresholdV, that.lowVoltageChargeLimitThresholdV) &&
                Objects.equals(lowVoltageChargeLimitMultiplierC, that.lowVoltageChargeLimitMultiplierC) &&
                Objects.equals(maxPerModuleChargeVoltage, that.maxPerModuleChargeVoltage);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                maxChargeVoltage, minDischargeVoltage, maxChargeCurrent, maxDischargeCurrent,
                maxOffgridDischargeCurrent, minStateOfChargePercent, minOffgridSoCPercent,
                chargeVMaxOverride, chargeIMaxOverride, dischargeVMinOverride, dischargeIMaxOverride,
                soc0to1Override, warningCodeOverride, alarmCodeOverride, statusOverride,
                highSoCProtection0to1, highSoCProtectionIA, isLeadAcidSpecialOn,
                disableLowVoltageChargeLimit, lowVoltageChargeLimitThresholdV,
                lowVoltageChargeLimitMultiplierC, maxPerModuleChargeVoltage
        );
    }
}