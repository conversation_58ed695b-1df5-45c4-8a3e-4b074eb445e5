package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.BatteryManufacturerEnum;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.RelayID;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.MenuItem;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class ElectricalViewModel {

    // #region Generic Properties (not populated by Electrical Configuration)
    @JsonProperty("Serial")
    private String serial;

    @JsonProperty("ProductDefaults")
    private InstallationSpecification productDefaults;

    @JsonProperty("IsInSync")
    private Boolean isInSync;

    @JsonProperty("DeviceTimestamp")
    private ZonedDateTime deviceTimestamp;

    @JsonProperty("CloudTimestamp")
    private ZonedDateTime cloudTimestamp;

    @JsonProperty("IsUserReadOnly")
    private Boolean isUserReadOnly;

    @JsonProperty("IsGridOn")
    private Boolean isGridOn;

    @JsonProperty("IsACCoupledSupported")
    private Boolean isACCoupledSupported = false;

    @JsonProperty("IsWASafetyCountrySupported")
    private Boolean isWASafetyCountrySupported = false;

    @JsonProperty("SuccessMessage")
    private String successMessage;

    @JsonProperty("ErrorMessage")
    private String errorMessage;
    // #endregion

    /**
     * Whether or not the system is marked as an AC Coupled system
     */
    @JsonProperty("IsACCoupled")
    private Boolean isACCoupled = false;

    /**
     * The maximum power that the grid-tied inverter can export in Watts (W)
     */
    @JsonProperty("GridTieInverterExportPowerW")
    private Integer gridTieInverterExportPowerW = 0;

    /**
     * The minimum value that the user can set for gridTieInverterExportPowerW
     */
    @JsonProperty("GridTieInverterExportPowerMinW")
    private Double gridTieInverterExportPowerMinW = 0.0;

    /**
     * The maximum value that the user can set for gridTieInverterExportPowerW
     */
    @JsonProperty("GridTieInverterExportPowerMaxW")
    private Double gridTieInverterExportPowerMaxW = 5000.0;

    /**
     * The maximum power that can be exported to the grid. This is not the same as
     * the
     * maximum power that the inverter can export to the grid as it takes into
     * account any
     * grid tied inverters.
     */
    @JsonProperty("SiteLimitW")
    private Integer siteLimitW;

    /**
     * The minimum power that the user can set for siteLimitW
     */
    @JsonProperty("SiteLimitMinW")
    private Double siteLimitMinW = 0.0;

    /**
     * The maximum power that the user can set for siteLimitW
     */
    @JsonProperty("SiteLimitMaxW")
    private Double siteLimitMaxW = 5000.0;

    @JsonProperty("SafetyCountry")
    private ElectricalConfiguration.Country safetyCountry = ElectricalConfiguration.Country.Australia;

    @JsonProperty("BatteryModel")
    private ElectricalConfiguration.BatteryModel batteryModel = ElectricalConfiguration.BatteryModel.None;

    @JsonProperty("BatteryManufacturer")
    private ElectricalConfiguration.BatteryManufacturer batteryManufacturer = ElectricalConfiguration.BatteryManufacturer.None;

    @JsonProperty("BatteryCapacity")
    private Integer batteryCapacity;

    @JsonProperty("BatteryUnit")
    private Integer batteryUnit = 1;

    @JsonProperty("BatteryChargeVoltage")
    private Double batteryChargeVoltage = 55.0;

    @JsonProperty("BatteryChargeCurrent")
    private Double batteryChargeCurrent = 20.0;

    @JsonProperty("BatteryDischargeVoltage")
    private Double batteryDischargeVoltage = 40.0;

    @JsonProperty("BatteryDischargeCurrent")
    private Double batteryDischargeCurrent = 25.0;

    @JsonProperty("BatteryMinimumSoC")
    private Integer batteryMinimumSoC = 50;

    @JsonProperty("BatteryMinimumSoCOffGrid")
    private Integer batteryMinimumSoCOffGrid = 5;

    @JsonProperty("BatteryFloatVoltage")
    private Double batteryFloatVoltage = 0.0;

    @JsonProperty("BatteryFloatCurrent")
    private Double batteryFloatCurrent = 0.0;

    @JsonProperty("BatteryFloatTimeInMinutes")
    private Integer batteryFloatTimeInMinutes = 0;

    @JsonProperty("BatteryChargeCurrentOverride")
    private Double batteryChargeCurrentOverride;

    @JsonProperty("BatteryDischargeCurrentOverride")
    private Double batteryDischargeCurrentOverride;

    @JsonProperty("DredSubscribed")
    private Boolean dredSubscribed;

    @JsonProperty("EnableMobileNetwork")
    private Boolean enableMobileNetwork;

    @JsonProperty("MobileNetworkProviderProfile")
    private String mobileNetworkProviderProfile;

    @JsonProperty("ShouldLimitExportPower")
    private Boolean shouldLimitExportPower = false; // do we want to limit the export power of the inverter

    @JsonProperty("BackupOn")
    private Boolean backupOn = true;

    @JsonProperty("ShadowScan")
    private Boolean shadowScan = true;

    @JsonProperty("OffGridCharge")
    private Boolean offGridCharge = true;

    @JsonProperty("Leading")
    private Boolean leading = true; // true: leading, false: lagging

    @JsonProperty("LeadingPowerFactor")
    private Double leadingPowerFactor = 1.0;

    @JsonProperty("LaggingPowerFactor")
    private Double laggingPowerFactor = 0.99;

    @JsonProperty("RelayConnections")
    private List<Boolean> relayConnections = Arrays.asList();

    @JsonProperty("RelayNames")
    private List<String> relayNames = Arrays.asList();

    @JsonProperty("BMSDetected")
    private Boolean bmsDetected = false;


    @JsonProperty("BatteryManufacturerFullMenus")
    private List<MenuItemVO> batteryManufacturerFullMenus = MenuItem.getBatteryManufacturerFullMenus();

    @JsonProperty("BatteryManufacturerMenus")
    private List<MenuItemVO> batteryManufacturerMenus = MenuItem.getBatteryManufacturerMenus();

    @JsonProperty("BatteryModelList")
    private List<MenuItemVO> batteryModelList = MenuItem.getBatteryModelFullMenus();

    @JsonProperty("BatteryModelFullList")
    private  List<MenuItemVO> batteryModelFullList = MenuItem.getBatteryModelFullMenus();


    // 0.8~1 (if -1, set it to 1)
    @JsonProperty("PowerFactor")
    public Double getPowerFactor() {
        return Optional.ofNullable(leading).orElse(true)
                ? Optional.ofNullable(leadingPowerFactor).orElse(1.0)
                : -Optional.ofNullable(laggingPowerFactor).orElse(0.99);
    }

    // Enablers
    @JsonProperty("ApplyFloatSettings")
    private Boolean applyFloatSettings = false;

    // #region Legacy
    /**
     * This is legacy Battery type up to ROSS 1.0.0
     * 
     * This should not be used any more.
     */
    @JsonProperty("BatteryType")
    private ElectricalConfiguration.BattType batteryType = ElectricalConfiguration.BattType.None;

    // Note: Menu lists would be handled by separate service classes in Java
    // public List<SelectListItem> SafetyCountryList { get; set; } =
    // Menus.GetCountryMenus;
    // public List<SelectListItem> MobileConnectionList { get; set; } =
    // Menus.MobileConnectionList;
    // etc.
    // #endregion

    /**
     * This method will apply the settings from the ElectricalViewModel and apply
     * them on top
     * of an existing ElectricalConfigurationVO, such as when the user applies their
     * changes.
     */
    public void applyChangesToConfiguration(ElectricalConfiguration config) {
        if (config == null) {
            throw new IllegalArgumentException("There is no current Installer Settings available");
        }

        config.setSafetyCountry(safetyCountry);
        config.setBatteryCapacity(batteryCapacity);

        if (Optional.ofNullable(config.getBmsDetected()).orElse(false)) {
            // BMS Detected, therefore BMS provides majority of details
            config.setChargeCurrentOverride(batteryChargeCurrentOverride);
            config.setDischargeCurrentOverride(batteryDischargeCurrentOverride);
            // BMS detected batteries can also have model selection
            config.setBatteryManufacturer(batteryManufacturer);
            config.setBatteryModel(batteryModel);
        } else {
            // BMS not detected, user must specify battery details
            config.setBatteryType(batteryType);
            config.setBatteryManufacturer(batteryManufacturer);
            config.setBatteryModel(batteryModel);

            var chargeSettings = new ElectricalConfiguration.BattChargeSettings();
            chargeSettings.setVoltage(batteryChargeVoltage);
            chargeSettings.setCurrent(batteryChargeCurrent);
            config.setBatteryChargeSettingsCustom(chargeSettings);

            var dischargeSettings = new ElectricalConfiguration.BattChargeSettings();
            dischargeSettings.setVoltage(batteryDischargeVoltage) ;
            dischargeSettings.setCurrent(batteryDischargeCurrent);
            config.setBatteryDischargeSettingsCustom(dischargeSettings);

            var floatSettings = new ElectricalConfiguration.BattFloatSettings();
            floatSettings.setVoltage(Optional.ofNullable(applyFloatSettings).orElse(false) ? batteryFloatVoltage : 0.0);
            floatSettings.setCurrent(Optional.ofNullable(applyFloatSettings).orElse(false) ? batteryFloatCurrent : 0.0);
            floatSettings.setTime(Optional.ofNullable(applyFloatSettings).orElse(false) ? batteryFloatTimeInMinutes : 0);
            config.setSetBatteryFloat(floatSettings);

            config.setFloatSettingsEnable(applyFloatSettings);
        }

        config.setMinimumSoC(batteryMinimumSoC);
        config.setMinimumSoCOffGrid(batteryMinimumSoCOffGrid);
        config.setDredSubscribed(dredSubscribed);
        config.setRetrofitMode(isACCoupled);

        // Currently hidden by Bug 10265
        // config.setEnableMobileNetwork(enableMobileNetwork);
        // config.setMobileNetworkProviderProfile(mobileNetworkProviderProfile);

        // Updates only the properties that the user has set.
        // This will not update LimitExportPower or SiteLimit to the correct values
        config.setIsLimitExportPower(shouldLimitExportPower);

        // all three of these values are set to site limit, but may get modified in a
        // later step
        config.setSiteLimit(siteLimitW);
        config.setLimitExportPowerUserValue(siteLimitW);
        config.setLimitExportPower(siteLimitW);
        config.setGridTieInverterMaxCapacity(gridTieInverterExportPowerW);

        config.setBackupOn(backupOn);
        config.setShadowScan(shadowScan);
        config.setOffGridCharge(offGridCharge);
        config.setPowerFactor(getPowerFactor());

        if (relayConnections != null) {
            config.setRelayConnections(relayConnections.toArray(new Boolean[0]));
        }
        if (relayNames != null) {
            config.setRelayNames(relayNames.toArray(new String[0]));
        }

        config.setDateTimeUpdated(ZonedDateTime.now());
    }

    public void populateFromElectricalConfiguration(ElectricalConfiguration config) {
        if (config == null) {
            throw new IllegalArgumentException("InstallerSettings is null");
        }

        // Initialize
        if (config.getRelayNames() == null || config.getRelayNames().length == 0 ||
                config.getRelayConnections() == null || config.getRelayConnections().length == 0) {
            relayNames = Arrays.stream(RelayID.values())
                    .map(RelayID::toString)
                    .collect(Collectors.toList());
            relayConnections = Arrays.stream(new Boolean[relayNames.size()])
                    .map(b -> false)
                    .collect(Collectors.toList());
        } else {
            relayNames = Arrays.asList(config.getRelayNames());
            relayConnections = Arrays.stream(config.getRelayConnections()).collect(Collectors.toList());
        }

        // Populate view model
        safetyCountry = Optional.ofNullable(config.getSafetyCountry()).orElse(safetyCountry);
        batteryType = Optional.ofNullable(config.getBatteryType()).orElse(batteryType);
        batteryModel = Optional.ofNullable(config.getBatteryModel()).orElse(batteryModel);
        batteryManufacturer = Optional.ofNullable(config.getBatteryManufacturer()).orElse(batteryManufacturer);
        batteryCapacity = Optional.ofNullable(config.getBatteryCapacity()).orElse(batteryCapacity);
        bmsDetected = Optional.ofNullable(config.getBmsDetected()).orElse(bmsDetected);

        if (Optional.ofNullable(bmsDetected).orElse(false)) {
            // Battery detected
            if (config.getBatteryChargeSettingsAuto() != null) {
                batteryChargeVoltage = config.getBatteryChargeSettingsAuto().getVoltageValue();
                batteryChargeCurrent = config.getBatteryChargeSettingsAuto().getCurrentValue();
                batteryChargeCurrentOverride = Optional.ofNullable(config.getChargeCurrentOverride())
                        .orElse(config.getBatteryChargeSettingsAuto().getCurrentValue());
            }
            if (config.getBatteryDischargeSettingsAuto() != null) {
                batteryDischargeVoltage = config.getBatteryDischargeSettingsAuto().getVoltageValue();
                batteryDischargeCurrent = config.getBatteryDischargeSettingsAuto().getCurrentValue();
                batteryDischargeCurrentOverride = Optional.ofNullable(config.getDischargeCurrentOverride())
                        .orElse(config.getBatteryDischargeSettingsAuto().getCurrentValue());
            }

            if (batteryManufacturer == ElectricalConfiguration.BatteryManufacturer.LG) {
                IBatteryDefault batteryDefault = getBatteryDefault(batteryModel);
                batteryUnit = (batteryDefault != null && batteryCapacity != null && batteryCapacity > 0
                        && batteryDefault.getCapacity() > 0)
                                ? batteryCapacity / batteryDefault.getCapacity()
                                : 1;
            }
        } else {
            if (config.getBatteryChargeSettingsCustom() != null) {
                batteryChargeVoltage = config.getBatteryChargeSettingsCustom().getVoltageValue();
                batteryChargeCurrent = config.getBatteryChargeSettingsCustom().getCurrentValue();
            }
            if (config.getBatteryDischargeSettingsCustom() != null) {
                batteryDischargeVoltage = config.getBatteryDischargeSettingsCustom().getVoltageValue();
                batteryDischargeCurrent = config.getBatteryDischargeSettingsCustom().getCurrentValue();
            }

            boolean hasSetBatteryFloat = config.getSetBatteryFloat() != null &&
                    (config.getSetBatteryFloat().getVoltageValue() + config.getSetBatteryFloat().getCurrentValue() > 0);
            applyFloatSettings = Optional.ofNullable(config.getFloatSettingsEnable()).orElse(applyFloatSettings)
                    && hasSetBatteryFloat;

            if (Optional.ofNullable(applyFloatSettings).orElse(false)) {
                batteryFloatVoltage = config.getSetBatteryFloat().getVoltageValue();
                batteryFloatCurrent = config.getSetBatteryFloat().getCurrentValue();
                batteryFloatTimeInMinutes = config.getSetBatteryFloat().getTimeValue();
            }

            // populate BatteryUnit based on battery model
            IBatteryDefault batteryDefault = getBatteryDefault(batteryModel);
            batteryUnit = (batteryDefault != null && batteryCapacity != null && batteryCapacity > 0
                    && batteryDefault.getCapacity() > 0)
                            ? batteryCapacity / batteryDefault.getCapacity()
                            : 1;
        }

        batteryMinimumSoC = Optional.ofNullable(config.getMinimumSoC()).orElse(batteryMinimumSoC);
        batteryMinimumSoCOffGrid = Optional.ofNullable(config.getMinimumSoCOffGrid()).orElse(batteryMinimumSoCOffGrid);
        dredSubscribed = Optional.ofNullable(config.getDredSubscribed()).orElse(dredSubscribed);
        isACCoupled = Optional.ofNullable(config.getRetrofitMode()).orElse(isACCoupled);
        enableMobileNetwork = Optional.ofNullable(config.getEnableMobileNetwork()).orElse(enableMobileNetwork);

        if (config.getMobileNetworkProviderProfile() != null && !config.getMobileNetworkProviderProfile().isEmpty()) {
            mobileNetworkProviderProfile = config.getMobileNetworkProviderProfile();
        }

        backupOn = Optional.ofNullable(config.getBackupOn()).orElse(backupOn);
        shadowScan = Optional.ofNullable(config.getShadowScan()).orElse(shadowScan);
        offGridCharge = Optional.ofNullable(config.getOffGridCharge()).orElse(offGridCharge);

        if (config.getPowerFactor() != null) {
            double pf = Math.abs(config.getPowerFactor());
            leading = config.getPowerFactor() > 0 || config.getPowerFactor() == -1;
            leadingPowerFactor = Optional.ofNullable(leading).orElse(true) ? pf : 0.0;
            laggingPowerFactor = Optional.ofNullable(leading).orElse(true) ? 0.99 : pf;
        }

        // Note: Menu handling would be done by separate service classes in Java
        if (bmsDetected) {
            batteryModelFullList = MenuItem.getBatteryModelMenus(batteryManufacturer);
        } else {
            if (batteryManufacturer == ElectricalConfiguration.BatteryManufacturer.Unknown) {
//                batteryManufacturerList = Menus.batteryManufacturerFullMenus;
            }
                batteryModelList = MenuItem.getBatteryModelMenus(batteryManufacturer);
        }

        Integer maxExportPower = Optional.ofNullable(productDefaults)
                .map(InstallationSpecification::getMaximumExportPowerInWatts)
                .orElse(0);

        // Set the Site Limit to the user value, if that doesn't exist then set to the
        // maximum export power
        gridTieInverterExportPowerW = Optional.ofNullable(config.getGridTieInverterMaxCapacity())
                .orElse(gridTieInverterExportPowerW);
        siteLimitW = Optional.ofNullable(config.getLimitExportPowerUserValue()).orElse(maxExportPower);

        // If ROSS supports the IsLimitExportPower Property then use that otherwise use
        // the old formula that relies on MaxExport Power capabilities
        shouldLimitExportPower = Optional.ofNullable(config.getIsLimitExportPower())
                .orElse(siteLimitW != null && siteLimitW < maxExportPower);

        // If this is an old version of ROSS and the limit is set to max - 1 bump it to
        // max for the UI.
        if (config.getIsLimitExportPower() == null && siteLimitW != null && siteLimitW.equals(maxExportPower - 1)) {
            siteLimitW = maxExportPower;
        }
    }

    private IBatteryDefault getBatteryDefault(ElectricalConfiguration.BatteryModel model) {
        if (model == null) {
            return null;
        }

        switch (model) {
            case Aquion_S20P:
                return new AquionS20PDefault();
            case Aquion_S30:
                return new AquionS30Default();
            case Aquion_Aspen_48S_2_2:
                return new AquionAspen48S2_2Default();
            case SimpliPHi_2_6:
                return new SimpliPHi_2_6Default();
            case SimpliPHi_3_4:
                return new SimpliPHi_3_4Default();
            case LG_M48063P3S:
                return new LGM48063P3SDefault();
            case LG_M48126P3S:
                return new LGM48126P3SDefault();
            default:
                return null;
        }
    }

    // Battery default interfaces and implementations would need to be created
    public interface IBatteryDefault {
        int getCapacity();
    }

    // These classes would need to be implemented with actual battery specifications
    public static class AquionS20PDefault implements IBatteryDefault {
        @Override
        public int getCapacity() {
            return 20; // placeholder value
        }
    }

    public static class AquionS30Default implements IBatteryDefault {
        @Override
        public int getCapacity() {
            return 30; // placeholder value
        }
    }

    public static class AquionAspen48S2_2Default implements IBatteryDefault {
        @Override
        public int getCapacity() {
            return 48; // placeholder value
        }
    }

    public static class SimpliPHi_2_6Default implements IBatteryDefault {
        @Override
        public int getCapacity() {
            return 26; // placeholder value
        }
    }

    public static class SimpliPHi_3_4Default implements IBatteryDefault {
        @Override
        public int getCapacity() {
            return 34; // placeholder value
        }
    }

    public static class LGM48063P3SDefault implements IBatteryDefault {
        @Override
        public int getCapacity() {
            return 63; // placeholder value
        }
    }

    public static class LGM48126P3SDefault implements IBatteryDefault {
        @Override
        public int getCapacity() {
            return 126; // placeholder value
        }
    }

    /**
     * 验证电气配置
     *
     * @param model 电气视图模型
     * @throws BizException 当验证失败时抛出包含所有错误信息的异常
     */
    public void validateElectricalConfiguration() {
        var model = this;
        List<String> errors = new ArrayList<>();

        if (model.getBatteryModel() != ElectricalConfiguration.BatteryModel.None) {
            // 电池容量验证
            if (model.getBatteryCapacity() != null) {
                if (model.getBatteryCapacity() < 1) {
                    errors.add("BatteryCapacity: Capacity (Ah) cannot be 0 or less.");
                } else if (model.getBatteryCapacity() > 9999) {
                    errors.add("BatteryCapacity: Capacity (Ah) cannot be more than 4 digits.");
                }
            }

            // 电池单元数量验证
            if (model.getBatteryUnit() != null) {
                if (model.getBatteryUnit() < 1) {
                    errors.add("BatteryUnit: Number of battery modules cannot be 0 or less.");
                } else if (model.getBatteryUnit() > 99) {
                    errors.add("BatteryUnit: Number of battery modules cannot be more than 2 digits.");
                }
            }

            // 电池充电电压验证
            if (model.getBatteryChargeVoltage() != null) {
                if (model.getBatteryChargeVoltage() < 40 || model.getBatteryChargeVoltage() > 60) {
                    errors.add("BatteryChargeVoltage: Charge Max Voltage must be within the range [40, 60].");
                }
            }

            // 电池充电电流验证
            if (model.getBatteryChargeCurrent() != null) {
                if (model.getBatteryChargeCurrent() < 0 || model.getBatteryChargeCurrent() > 85) {
                    errors.add("BatteryChargeCurrent: Charge Max Current must be within the range [0, 85].");
                }
            }

            // 电池放电电压验证
            if (model.getBatteryDischargeVoltage() != null) {
                if (model.getBatteryDischargeVoltage() < 40 || model.getBatteryDischargeVoltage() > 60) {
                    errors.add("BatteryDischargeVoltage: Discharge Max Voltage must be within the range [40, 60].");
                }
            }

            // 电池放电电流验证
            if (model.getBatteryDischargeCurrent() != null) {
                if (model.getBatteryDischargeCurrent() < 0 || model.getBatteryDischargeCurrent() > 100) {
                    errors.add("BatteryDischargeCurrent: Discharge Max Current must be within the range [0, 100].");
                }
            }

            // 浮充设置验证
            if (Boolean.TRUE.equals(model.getApplyFloatSettings())) {
                if (model.getBatteryFloatVoltage() != null) {
                    if (model.getBatteryFloatVoltage() < 40 || model.getBatteryFloatVoltage() > 60) {
                        errors.add("BatteryFloatVoltage: Float voltage must be within the range [40, 60].");
                    }
                }
                if (model.getBatteryFloatCurrent() != null) {
                    if (model.getBatteryFloatCurrent() < 0 || model.getBatteryFloatCurrent() > 5) {
                        errors.add("BatteryFloatCurrent: Float current must be within the range [0, 5].");
                    }
                }
            }

            // 最小SoC验证
            if (model.getBatteryMinimumSoC() != null) {
                if (model.getBatteryMinimumSoC() < 0 || model.getBatteryMinimumSoC() > 100) {
                    errors.add("BatteryMinimumSoC: Minimum SoC must be within the range [0, 100].");
                }
            }

            // 离网最小SoC验证
            if (model.getBatteryMinimumSoCOffGrid() != null) {
                if (model.getBatteryMinimumSoCOffGrid() < 0 || model.getBatteryMinimumSoCOffGrid() > 100) {
                    errors.add("BatteryMinimumSoCOffGrid: Minimum SoC offgrid must be within the range [0, 100].");
                }
            }
        }

        // 功率因数验证
        if (Boolean.TRUE.equals(model.getLeading())) {
            if (model.getLeadingPowerFactor() != null) {
                if (model.getLeadingPowerFactor() < 0.8 || model.getLeadingPowerFactor() > 1) {
                    errors.add("LeadingPowerFactor: Power factor is invalid.");
                }
            }
        } else {
            if (model.getLaggingPowerFactor() != null) {
                if (model.getLaggingPowerFactor() < 0.8 || model.getLaggingPowerFactor() >= 1) {
                    errors.add("LaggingPowerFactor: Power factor is invalid.");
                }
            }
        }

        // 移动网络验证
        if (Boolean.TRUE.equals(model.getEnableMobileNetwork()) &&
                (model.getMobileNetworkProviderProfile() == null || model.getMobileNetworkProviderProfile().trim().isEmpty())) {
            errors.add("MobileNetworkProviderProfile: Mobile Network Provider Profile is mandatory if Mobile Network is enabled.");
        }

        // 站点限制功率验证
        if (model.getSiteLimitW() != null && model.getProductDefaults() != null) {
            if (model.getSiteLimitW() < model.getProductDefaults().getMinimumSiteExportWatts()) {
                errors.add(String.format("LimitExportPower: Please enter a value greater than or equal to %d.",
                        model.getProductDefaults().getMinimumSiteExportWatts()));
            }

            if (model.getSiteLimitW() > model.getProductDefaults().getMaximumSiteExportWatts()) {
                errors.add(String.format("LimitExportPower: Please enter a value less than or equal to %d.",
                        model.getProductDefaults().getMaximumSiteExportWatts()));
            }
        }

        // 如果有验证错误，抛出异常
        if (!errors.isEmpty()) {
            String errorMessage = String.join("; ", errors);
            throw new BizException("Validation failed: " + errorMessage);
        }
    }
}