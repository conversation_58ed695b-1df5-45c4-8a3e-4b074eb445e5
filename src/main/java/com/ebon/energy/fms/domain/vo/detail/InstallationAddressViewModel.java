package com.ebon.energy.fms.domain.vo.detail;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Data
public class InstallationAddressViewModel implements IViewModelAddress {

    @JsonProperty("PlaceId")
    private String placeId;
    @JsonProperty("FullAddress")
    private String fullAddress;
    @JsonProperty("LocalityString")
    private String localityString;
    @JsonProperty("Latitude")
    private String latitude;
    @JsonProperty("Longitude")
    private String longitude;
    @JsonProperty("StreetNumber")
    private String streetNumber;
    @JsonProperty("StreetName")
    private String streetName;
    @JsonProperty("Suburb")
    private String suburb;
    @JsonProperty("State")
    private String state;
    @JsonProperty("PostCode")
    private String postCode;
    @JsonProperty("Country")
    private String country;

    // 额外字段（接口未声明）
    @JsonProperty("CountryStates")
    private List<CountryStateDTO> countryStates;

    @JsonProperty("TimeZoneId")
    private String timeZoneId;

    // 只读计算属性
    @Override
    @JsonProperty("AddressString")
    public String getAddressString() {
        String streetNum = Objects.toString(streetNumber, "");
        String streetNm  = Objects.toString(streetName, "");
        String sb = Objects.toString(suburb, "");
        String st = Objects.toString(state, "");
        String pc = Objects.toString(postCode, "");
        String c  = Objects.toString(country, "");
        if (streetNm.isEmpty()) {
            return String.format("%s %s, %s, %s, %s", streetNum, sb, st, pc, c);
        } else {
            return String.format("%s %s %s, %s, %s, %s", streetNum, streetNm, sb
, st, pc, c);
        }
    }

    // 可空链路的判断使用 Optional 封装调用
    @JsonProperty("IsNewZealandAddress")
    public boolean isNewZealandAddress() {
        return Optional.ofNullable(country)
                .map(String::toLowerCase)
                .map(c -> "new zealand".equals(c) || "nz".equals(c))
                .orElse(false);
    }

    @JsonProperty("IsAustralianAddress")
    public boolean isAustralianAddress() {
        return Optional.ofNullable(country)
                .map(String::toLowerCase)
                .map(c -> "australia".equals(c) || "au".equals(c))
                .orElse(false);
    }
}