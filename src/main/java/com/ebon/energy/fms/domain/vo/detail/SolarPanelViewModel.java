package com.ebon.energy.fms.domain.vo.detail;

import com.ebon.energy.fms.common.enums.PanelDirection;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SolarPanelViewModel {
    @JsonProperty("ArrayName")
    private String arrayName;
    @JsonProperty("NumberOfPanels")
    private Integer numberOfPanels;   // 对应 C# int?
    @JsonProperty("PanelDirection")
    private PanelDirection panelDirection; // 对应 C# PanelDirection?
    @JsonProperty("PvSize")
    private Double pvSize;            // 对应 C# double?
}