package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.BattType;
import com.ebon.energy.fms.common.enums.BatteryManufacturerEnum;
import com.ebon.energy.fms.common.enums.BatteryModelEnum;
import com.ebon.energy.fms.common.enums.FanMode;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.Country;
import com.google.gson.Gson;

import java.time.ZonedDateTime;

public class ElectricalConfigurationVO implements Cloneable {

    private String SchemaVersion;
    private Integer GroupEventId;
    private Integer LimitExportPower;
    private Boolean IsLimitExportPower;
    private Integer LimitExportPowerUserValue;
    private Integer SiteLimit;
    private Integer GridTieInverterMaxCapacity;
    private Boolean BackupOn = true;
    private Boolean ShadowScan;
    private Country SafetyCountry;
    private Boolean OffGridCharge;
    private BattChargeSettingsVO BatteryChargeSettingsAuto;
    private BattChargeSettingsVO BatteryDischargeSettingsAuto;
    private BattChargeSettingsVO BatteryChargeSettingsCustom;
    private BattChargeSettingsVO BatteryDischargeSettingsCustom;
    private Double ChargeCurrentOverride;
    private Double DischargeCurrentOverride;
    private Integer MinimumSoC;
    private Integer MinimumSoCOffGrid;
    private ZonedDateTime SetTime;
    private Double PowerFactor;
    private Boolean FloatSettingsEnable;
    private BattFloatSettingsVO SetBatteryFloat;
    private Boolean DredSubscribed;
    private Integer BatteryCapacity;
    private Boolean[] RelayConnections;
    private String[] RelayNames;
    private Boolean IoTConnectionSetup;
    private Boolean EnableMobileNetwork;
    private String MobileNetworkProviderProfile;
    private Boolean BMSDetected;
    private Boolean UpdateFirmware;
    private Boolean RetrofitMode;
    private ZonedDateTime DateTimeUpdated;
    private FanMode fanMode;
    private BatteryManufacturerEnum BatteryManufacturer;
    private BatteryModelEnum BatteryModel;
    private BattType BatteryType;

    public static ElectricalConfigurationVO fromJson(String json) {
        Gson gson = new Gson();
        return gson.fromJson(json, ElectricalConfigurationVO.class);
    }

    public String getSchemaVersion() {
        return SchemaVersion;
    }

    public void setSchemaVersion(String schemaVersion) {
        SchemaVersion = schemaVersion;
    }

    public Integer getGroupEventId() {
        return GroupEventId;
    }

    public void setGroupEventId(Integer groupEventId) {
        GroupEventId = groupEventId;
    }

    public Integer getLimitExportPower() {
        return LimitExportPower;
    }

    public void setLimitExportPower(Integer limitExportPower) {
        LimitExportPower = limitExportPower;
    }

    public Boolean getIsLimitExportPower() {
        return IsLimitExportPower;
    }

    public void setIsLimitExportPower(Boolean isLimitExportPower) {
        IsLimitExportPower = isLimitExportPower;
    }

    public Integer getLimitExportPowerUserValue() {
        return LimitExportPowerUserValue;
    }

    public void setLimitExportPowerUserValue(Integer limitExportPowerUserValue) {
        LimitExportPowerUserValue = limitExportPowerUserValue;
    }

    public Integer getSiteLimit() {
        return SiteLimit;
    }

    public void setSiteLimit(Integer siteLimit) {
        SiteLimit = siteLimit;
    }

    public Integer getGridTieInverterMaxCapacity() {
        return GridTieInverterMaxCapacity;
    }

    public void setGridTieInverterMaxCapacity(Integer gridTieInverterMaxCapacity) {
        GridTieInverterMaxCapacity = gridTieInverterMaxCapacity;
    }

    public Boolean getBackupOn() {
        return BackupOn;
    }

    public void setBackupOn(Boolean backupOn) {
        BackupOn = backupOn;
    }

    public Boolean getShadowScan() {
        return ShadowScan;
    }

    public void setShadowScan(Boolean shadowScan) {
        ShadowScan = shadowScan;
    }

    public Country getSafetyCountry() {
        return SafetyCountry;
    }

    public void setSafetyCountry(Country safetyCountry) {
        SafetyCountry = safetyCountry;
    }

    public Boolean getOffGridCharge() {
        return OffGridCharge;
    }

    public void setOffGridCharge(Boolean offGridCharge) {
        OffGridCharge = offGridCharge;
    }

    public BattChargeSettingsVO getBatteryChargeSettingsAuto() {
        return BatteryChargeSettingsAuto;
    }

    public void setBatteryChargeSettingsAuto(BattChargeSettingsVO batteryChargeSettingsAuto) {
        BatteryChargeSettingsAuto = batteryChargeSettingsAuto;
    }

    public BattChargeSettingsVO getBatteryDischargeSettingsAuto() {
        return BatteryDischargeSettingsAuto;
    }

    public void setBatteryDischargeSettingsAuto(BattChargeSettingsVO batteryDischargeSettingsAuto) {
        BatteryDischargeSettingsAuto = batteryDischargeSettingsAuto;
    }

    public BattChargeSettingsVO getBatteryChargeSettingsCustom() {
        return BatteryChargeSettingsCustom;
    }

    public void setBatteryChargeSettingsCustom(BattChargeSettingsVO batteryChargeSettingsCustom) {
        BatteryChargeSettingsCustom = batteryChargeSettingsCustom;
    }

    public BattChargeSettingsVO getBatteryDischargeSettingsCustom() {
        return BatteryDischargeSettingsCustom;
    }

    public void setBatteryDischargeSettingsCustom(BattChargeSettingsVO batteryDischargeSettingsCustom) {
        BatteryDischargeSettingsCustom = batteryDischargeSettingsCustom;
    }

    public Double getChargeCurrentOverride() {
        return ChargeCurrentOverride;
    }

    public void setChargeCurrentOverride(Double chargeCurrentOverride) {
        ChargeCurrentOverride = chargeCurrentOverride;
    }

    public Double getDischargeCurrentOverride() {
        return DischargeCurrentOverride;
    }

    public void setDischargeCurrentOverride(Double dischargeCurrentOverride) {
        DischargeCurrentOverride = dischargeCurrentOverride;
    }

    public Integer getMinimumSoC() {
        return MinimumSoC;
    }

    public void setMinimumSoC(Integer minimumSoC) {
        MinimumSoC = minimumSoC;
    }

    public Integer getMinimumSoCOffGrid() {
        return MinimumSoCOffGrid;
    }

    public void setMinimumSoCOffGrid(Integer minimumSoCOffGrid) {
        MinimumSoCOffGrid = minimumSoCOffGrid;
    }

    public ZonedDateTime getSetTime() {
        return SetTime;
    }

    public void setSetTime(ZonedDateTime setTime) {
        SetTime = setTime;
    }

    public Double getPowerFactor() {
        return PowerFactor;
    }

    public void setPowerFactor(Double powerFactor) {
        PowerFactor = powerFactor;
    }

    public Boolean getFloatSettingsEnable() {
        return FloatSettingsEnable;
    }

    public void setFloatSettingsEnable(Boolean floatSettingsEnable) {
        FloatSettingsEnable = floatSettingsEnable;
    }

    public BattFloatSettingsVO getSetBatteryFloat() {
        return SetBatteryFloat;
    }

    public void setSetBatteryFloat(BattFloatSettingsVO setBatteryFloat) {
        SetBatteryFloat = setBatteryFloat;
    }

    public Boolean getDredSubscribed() {
        return DredSubscribed;
    }

    public void setDredSubscribed(Boolean dredSubscribed) {
        DredSubscribed = dredSubscribed;
    }

    public Integer getBatteryCapacity() {
        return BatteryCapacity;
    }

    public void setBatteryCapacity(Integer batteryCapacity) {
        BatteryCapacity = batteryCapacity;
    }

    public Boolean[] getRelayConnections() {
        return RelayConnections;
    }

    public void setRelayConnections(Boolean[] relayConnections) {
        RelayConnections = relayConnections;
    }

    public String[] getRelayNames() {
        return RelayNames;
    }

    public void setRelayNames(String[] relayNames) {
        RelayNames = relayNames;
    }

    public Boolean getIoTConnectionSetup() {
        return IoTConnectionSetup;
    }

    public void setIoTConnectionSetup(Boolean iotConnectionSetup) {
        IoTConnectionSetup = iotConnectionSetup;
    }

    public Boolean getEnableMobileNetwork() {
        return EnableMobileNetwork;
    }

    public void setEnableMobileNetwork(Boolean enableMobileNetwork) {
        EnableMobileNetwork = enableMobileNetwork;
    }

    public String getMobileNetworkProviderProfile() {
        return MobileNetworkProviderProfile;
    }

    public void setMobileNetworkProviderProfile(String mobileNetworkProviderProfile) {
        MobileNetworkProviderProfile = mobileNetworkProviderProfile;
    }

    public Boolean getBMSDetected() {
        return BMSDetected;
    }

    public void setBMSDetected(Boolean bmsDetected) {
        BMSDetected = bmsDetected;
    }

    public Boolean getUpdateFirmware() {
        return UpdateFirmware;
    }

    public void setUpdateFirmware(Boolean updateFirmware) {
        UpdateFirmware = updateFirmware;
    }

    public Boolean getRetrofitMode() {
        return RetrofitMode;
    }

    public void setRetrofitMode(Boolean retrofitMode) {
        RetrofitMode = retrofitMode;
    }

    public ZonedDateTime getDateTimeUpdated() {
        return DateTimeUpdated;
    }

    public void setDateTimeUpdated(ZonedDateTime dateTimeUpdated) {
        DateTimeUpdated = dateTimeUpdated;
    }

    public FanMode getFanMode() {
        return fanMode;
    }

    public void setFanMode(FanMode fanMode) {
        this.fanMode = fanMode;
    }

    public BatteryManufacturerEnum getBatteryManufacturer() {
        return BatteryManufacturer;
    }

    public void setBatteryManufacturer(BatteryManufacturerEnum batteryManufacturer) {
        BatteryManufacturer = batteryManufacturer;
    }

    public BatteryModelEnum getBatteryModel() {
        return BatteryModel;
    }

    public void setBatteryModel(BatteryModelEnum batteryModel) {
        BatteryModel = batteryModel;
    }

    public BattType getBatteryType() {
        return BatteryType;
    }

    public void setBatteryType(BattType batteryType) {
        BatteryType = batteryType;
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        ElectricalConfigurationVO clone = (ElectricalConfigurationVO) super.clone();
        if (DateTimeUpdated != null) {
            clone.DateTimeUpdated = DateTimeUpdated.withZoneSameInstant(DateTimeUpdated.getZone());
        }
        if (SetTime != null) {
            clone.SetTime = SetTime.withZoneSameInstant(SetTime.getZone());
        }
        clone.BatteryChargeSettingsAuto = BatteryChargeSettingsAuto != null ? (BattChargeSettingsVO) BatteryChargeSettingsAuto.clone() : null;
        clone.BatteryDischargeSettingsAuto = BatteryDischargeSettingsAuto != null ? (BattChargeSettingsVO) BatteryDischargeSettingsAuto.clone() : null;
        clone.BatteryChargeSettingsCustom = BatteryChargeSettingsCustom != null ? (BattChargeSettingsVO) BatteryChargeSettingsCustom.clone() : null;
        clone.BatteryDischargeSettingsCustom = BatteryDischargeSettingsCustom != null ? (BattChargeSettingsVO) BatteryDischargeSettingsCustom.clone() : null;
        clone.SetBatteryFloat = SetBatteryFloat != null ? (BattFloatSettingsVO) SetBatteryFloat.clone() : null;
        clone.RelayConnections = RelayConnections != null ? RelayConnections.clone() : null;
        clone.RelayNames = RelayNames != null ? RelayNames.clone() : null;
        return clone;
    }

    public void patchLegacyBatteryInfo() {
        BattType battType = BatteryType == null ? BattType.None : BatteryType;
        BatteryManufacturerEnum batteryManufacturer = BatteryManufacturer == null ? BatteryManufacturerEnum.None : BatteryManufacturer;
        if (battType.ordinal() != 0 && batteryManufacturer.ordinal() == 0) {
            switch (battType) {
                case None:
                    BatteryManufacturer = BatteryManufacturerEnum.None;
                    BatteryModel = BatteryModelEnum.None;
                    break;
                case Unknown:
                    BatteryManufacturer = BatteryManufacturerEnum.Unknown;
                    BatteryModel = BatteryModelEnum.Unknown;
                    break;
                case Custom:
                    BatteryManufacturer = BatteryManufacturerEnum.Custom;
                    BatteryModel = BatteryModelEnum.Custom;
                    break;
                case LG_Resu_V1:
                case _LG_V2:
                    BatteryManufacturer = BatteryManufacturerEnum.LG;
                    BatteryModel = BatteryModelEnum.LG_RESU_6_5;
                    break;
                case Pylon_US2000A:
                case _Pylon_V2:
                    BatteryManufacturer = BatteryManufacturerEnum.Pylon;
                    BatteryModel = BatteryModelEnum.Pylon_US2000A;
                    break;
                case Lead_Acid_Flooded:
                    BatteryManufacturer = BatteryManufacturerEnum.LeadAcid;
                    BatteryModel = BatteryModelEnum.LeadAcid_Flooded;
                    break;
                case Lead_Acid_Sealed:
                    BatteryManufacturer = BatteryManufacturerEnum.LeadAcid;
                    BatteryModel = BatteryModelEnum.LeadAcid_Sealed;
                    break;
                case SimpliPHi_2_6:
                    BatteryManufacturer = BatteryManufacturerEnum.SimpliPHi;
                    BatteryModel = BatteryModelEnum.SimpliPHi_2_6;
                    break;
                case SimpliPHi_3_4:
                    BatteryManufacturer = BatteryManufacturerEnum.SimpliPHi;
                    BatteryModel = BatteryModelEnum.SimpliPHi_3_4;
                    break;
                case Redflow_V1:
                    BatteryManufacturer = BatteryManufacturerEnum.Redflow;
                    BatteryModel = BatteryModelEnum.Redflow_V1;
                    break;
                case AquionAspen48S2_2:
                    BatteryManufacturer = BatteryManufacturerEnum.Aquion;
                    BatteryModel = BatteryModelEnum.Aquion_Aspen_48S_2_2;
                    break;
                case AquionS20P:
                    BatteryManufacturer = BatteryManufacturerEnum.Aquion;
                    BatteryModel = BatteryModelEnum.Aquion_S20P;
                    break;
                case AquionS30:
                    BatteryManufacturer = BatteryManufacturerEnum.Aquion;
                    BatteryModel = BatteryModelEnum.Aquion_S30;
                    break;
            }
        } else if (battType.ordinal() == 0 && batteryManufacturer.ordinal()!=0) {
            BatteryModelEnum batteryModel = BatteryModel==null?BatteryModelEnum.None:BatteryModel;
            switch (batteryModel) {
                case None:
                    BatteryType = BattType.None;
                    break;
                case Unknown:
                    BatteryType = BattType.Unknown;
                    break;
                case Custom:
                    BatteryType = BattType.Custom;
                    break;
                case LG_RESU_6_4Ex:
                case LG_RESU_3_3:
                case LG_RESU_6_5:
                case LG_RESU_10:
                case LG_RESU_Plus:
                case LG_M48063P3S:
                case LG_M48126P3S:
                    BatteryType = BattType.LG_Resu_V1;
                    break;
                case Pylon_US2000A:
                case Pylon_US2000B:
                    BatteryType = BattType.Pylon_US2000A;
                    break;
                case LeadAcid_Flooded:
                    BatteryType = BattType.Lead_Acid_Flooded;
                    break;
                case LeadAcid_Sealed:
                    BatteryType = BattType.Lead_Acid_Sealed;
                    break;
                case SimpliPHi_2_6:
                    BatteryType = BattType.SimpliPHi_2_6;
                    break;
                case SimpliPHi_3_4:
                    BatteryType = BattType.SimpliPHi_3_4;
                    break;
                case Redflow_V1:
                    BatteryType = BattType.Redflow_V1;
                    break;
                case Aquion_Aspen_48S_2_2:
                    BatteryType = BattType.AquionAspen48S2_2;
                    break;
                case Aquion_S20P:
                    BatteryType = BattType.AquionS20P;
                    break;
                case Aquion_S30:
                    BatteryType = BattType.AquionS30;
                    break;
            }
        }
    }
}