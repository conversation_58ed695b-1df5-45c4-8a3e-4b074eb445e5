package com.ebon.energy.fms.domain.vo;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Optional;

/**
 * 电气配置类
 * 从C#的ElectricalConfiguration类转换而来
 */
@Data
public class ElectricalConfiguration {

    @JsonProperty("SchemaVersion")
    private String schemaVersion;

    @JsonProperty("GroupEventId")
    private Integer groupEventId;

    /**
     * Messages are now set depending on the product model by the View Controller
     */
    @JsonProperty("LimitExportPower")
    private Integer limitExportPower;

    /**
     * Do we want to limit the export power of the inverter.
     * This property will be available on ROSS version 1.5.7.5 or later. To remove
     * the limit on the inverter for older versions
     * the maximum nameplate export power capacity is sent as well as this value.
     */
    @JsonProperty("IsLimitExportPower")
    private Boolean isLimitExportPower;

    /**
     * The value User set in the setting.
     * This property will be available on ROSS version 1.5.7.5 or later. If the user
     * sets the limit export power to the max
     * this property will be used to store the user value. The value sent to the
     * inverter will be either max power - 1 or maxpower. The pivot is
     * the IsLimitExportPower property. If this true the max power -1 will be sent,
     * if it is false the maxpower value will be sent. On the read side if
     * IsLimitExportPower is false this property will be injected into
     * LimitExportPower.
     */
    @JsonProperty("LimitExportPowerUserValue")
    private Integer limitExportPowerUserValue;

    /**
     * For an AC Coupled system, this represents the maximum amount of power that
     * can be exported to the grid.
     * This number includes both the power exported by the grid-tied inverter and
     * the Redback inverter.
     * This value is in Watts (W)
     */
    @JsonProperty("SiteLimit")
    private Integer siteLimit;

    /**
     * For an AC Coupled system, this represents the maximum amount of power that
     * the grid-tied inverter could
     * export. This is used when we need to keep under the site limit and want to
     * know how to limit the Redback
     * inverter such that it does not exceed the site limit.
     * This value is in Watts (W)
     */
    @JsonProperty("GridTieInverterMaxCapacity")
    private Integer gridTieInverterMaxCapacity;

    /**
     * Backup circuit on/off switch. Defaulted to true.
     */
    @JsonProperty("BackupOn")
    private Boolean backupOn = true;

    /**
     * Default to true
     */
    @JsonProperty("ShadowScan")
    private Boolean shadowScan;

    /**
     * Default Australia or Factory
     */
    @JsonProperty("SafetyCountry")
    private Country safetyCountry;

    /**
     * Default true
     */
    @JsonProperty("OffGridCharge")
    private Boolean offGridCharge;

    /**
     * auto set, do not show if BMSDetected == false
     */
    @JsonProperty("BatteryChargeSettingsAuto")
    private BattChargeSettings batteryChargeSettingsAuto;

    /**
     * auto set, do not show if BMSDetected == false
     */
    @JsonProperty("BatteryDischargeSettingsAuto")
    private BattChargeSettings batteryDischargeSettingsAuto;

    /**
     * only show if BMSDetected == false && BattType != None : voltage default 55,
     * range (40, 60). Current default 20, range (0, 85)
     */
    @JsonProperty("BatteryChargeSettingsCustom")
    private BattChargeSettings batteryChargeSettingsCustom;

    /**
     * only show if BMSDetected == false && BattType != None : voltage default 40,
     * range (40, 60), Current default 25, range (0, 100)
     */
    @JsonProperty("BatteryDischargeSettingsCustom")
    private BattChargeSettings batteryDischargeSettingsCustom;

    /**
     * auto set to == BatteryChargeSettingsAuto.ChargeParam : do not show if
     * BattChargeSettingsCustom is shown
     */
    @JsonProperty("ChargeCurrentOverride")
    private Double chargeCurrentOverride;

    /**
     * auto set to == BatteryDischargeSettingsAuto.ChargeParam : do not show if
     * BattDischargeSettingsCustom is shown
     */
    @JsonProperty("DischargeCurrentOverride")
    private Double dischargeCurrentOverride;

    /**
     * Public use, please show, default 50, range (0, 100)
     */
    @Min(0)
    @Max(100)
    @JsonProperty("MinimumSoC")
    private Integer minimumSoC;

    /**
     * Public use, please show, default 5, range (0, 100)
     */
    @Min(0)
    @Max(100)
    @JsonProperty("MinimumSoCOffGrid")
    private Integer minimumSoCOffGrid;

    @JsonProperty("SetTime")
    private LocalDateTime setTime;

    /**
     * public use, accepts -0.99 -> -0.8 or 0.8 -> 1 : default 1
     */
    @JsonProperty("PowerFactor")
    private Double powerFactor;

    /**
     * Do not show if BMSDetect == true
     */
    @JsonProperty("FloatSettingsEnable")
    private Boolean floatSettingsEnable;

    /**
     * Do not show if BMSDetect == true, editable if BattType == Custom
     */
    @JsonProperty("SetBatteryFloat")
    private BattFloatSettings setBatteryFloat;

    @JsonProperty("DredSubscribed")
    private Boolean dredSubscribed;

    /**
     * In Ah.
     * For multiple batteries this is the sum of the individual battery capacities
     * (-joey circa April 2017)
     */
    @JsonProperty("BatteryCapacity")
    private Integer batteryCapacity;

    @JsonProperty("RelayConnections")
    private Boolean[] relayConnections;

    @JsonProperty("RelayNames")
    private String[] relayNames;

    @JsonProperty("IoTConnectionSetup")
    private Boolean ioTConnectionSetup;

    @JsonProperty("EnableMobileNetwork")
    private Boolean enableMobileNetwork;

    @JsonProperty("MobileNetworkProviderProfile")
    private String mobileNetworkProviderProfile;

    /**
     * auto set
     */
    @JsonProperty("BMSDetected")
    private Boolean bmsDetected;

    /**
     * special : show in separate page
     */
    @JsonProperty("UpdateFirmware")
    private Boolean updateFirmware;

    /**
     * Puts the inverter into retrofit mode, in case the house has a power generator
     */
    @JsonProperty("RetrofitMode")
    private Boolean retrofitMode;

    /**
     * the time when an installer setting got applied
     * auto set, not shown
     */
    @JsonProperty("DateTimeUpdated")
    private ZonedDateTime dateTimeUpdated;

    /**
     * default auto
     */
    @JsonProperty("fanMode")
    private FanMode fanMode;

    @JsonProperty("BatteryManufacturer")
    private BatteryManufacturer batteryManufacturer;

    @JsonProperty("BatteryModel")
    private BatteryModel batteryModel;

    /**
     * This is legacy Battery type up to ROSS 1.0.0
     * This should not be used any more.
     */
    @JsonProperty("BatteryType")
    private BattType batteryType;

    /**
     * Clones all properties from this instance of ElectricalConfiguration including
     * any sub-classes and return it
     *
     * @return cloned object of this instance of ElectricalConfiguration
     */
    @Override
    public Object clone() throws CloneNotSupportedException {
        ElectricalConfiguration clone = (ElectricalConfiguration) super.clone();

        if (dateTimeUpdated != null) {
            clone.dateTimeUpdated = dateTimeUpdated;
        }

        if (setTime != null) {
            clone.setTime = setTime;
        }

        if (batteryChargeSettingsAuto != null) {
            clone.batteryChargeSettingsAuto = (BattChargeSettings) batteryChargeSettingsAuto.clone();
        }
        if (batteryDischargeSettingsAuto != null) {
            clone.batteryDischargeSettingsAuto = (BattChargeSettings) batteryDischargeSettingsAuto.clone();
        }
        if (batteryChargeSettingsCustom != null) {
            clone.batteryChargeSettingsCustom = (BattChargeSettings) batteryChargeSettingsCustom.clone();
        }
        if (batteryDischargeSettingsCustom != null) {
            clone.batteryDischargeSettingsCustom = (BattChargeSettings) batteryDischargeSettingsCustom.clone();
        }
        if (setBatteryFloat != null) {
            clone.setBatteryFloat = (BattFloatSettings) setBatteryFloat.clone();
        }
        if (relayConnections != null) {
            clone.relayConnections = Arrays.copyOf(relayConnections, relayConnections.length);
        }
        if (relayNames != null) {
            clone.relayNames = Arrays.copyOf(relayNames, relayNames.length);
        }
        return clone;
    }

    /**
     * Syncs any legacy properties to support both backward and forward
     * compatibilities
     */
    public void patchLegacyBatteryInfo() {
        if (batteryType != null && batteryType != BattType.None
                && (batteryManufacturer == null || batteryManufacturer == BatteryManufacturer.None)) {

            switch (batteryType) {
                case None:
                    batteryManufacturer = BatteryManufacturer.None;
                    batteryModel = BatteryModel.None;
                    break;
                case Unknown:
                    batteryManufacturer = BatteryManufacturer.Unknown;
                    batteryModel = BatteryModel.Unknown;
                    break;
                case Custom:
                    batteryManufacturer = BatteryManufacturer.Custom;
                    batteryModel = BatteryModel.Custom;
                    break;
                case LG_Resu_V1:
                case _LG_V2:
                    batteryManufacturer = BatteryManufacturer.LG;
                    batteryModel = BatteryModel.LG_RESU_6_5;
                    break;
                case Pylon_US2000A:
                case _Pylon_V2:
                    batteryManufacturer = BatteryManufacturer.Pylon;
                    batteryModel = BatteryModel.Pylon_US2000A;
                    break;
                case Lead_Acid_Flooded:
                    batteryManufacturer = BatteryManufacturer.LeadAcid;
                    batteryModel = BatteryModel.LeadAcid_Flooded;
                    break;
                case Lead_Acid_Sealed:
                    batteryManufacturer = BatteryManufacturer.LeadAcid;
                    batteryModel = BatteryModel.LeadAcid_Sealed;
                    break;
                case SimpliPHi_2_6:
                    batteryManufacturer = BatteryManufacturer.SimpliPHi;
                    batteryModel = BatteryModel.SimpliPHi_2_6;
                    break;
                case SimpliPHi_3_4:
                    batteryManufacturer = BatteryManufacturer.SimpliPHi;
                    batteryModel = BatteryModel.SimpliPHi_3_4;
                    break;
                case Redflow_V1:
                    batteryManufacturer = BatteryManufacturer.Redflow;
                    batteryModel = BatteryModel.Redflow_V1;
                    break;
                case AquionAspen48S2_2:
                    batteryManufacturer = BatteryManufacturer.Aquion;
                    batteryModel = BatteryModel.Aquion_Aspen_48S_2_2;
                    break;
                case AquionS20P:
                    batteryManufacturer = BatteryManufacturer.Aquion;
                    batteryModel = BatteryModel.Aquion_S20P;
                    break;
                case AquionS30:
                    batteryManufacturer = BatteryManufacturer.Aquion;
                    batteryModel = BatteryModel.Aquion_S30;
                    break;
            }
        } else if ((batteryType == null || batteryType == BattType.None)
                && batteryManufacturer != null && batteryManufacturer != BatteryManufacturer.None) {

            if (batteryModel != null) {
                switch (batteryModel) {
                    case None:
                        batteryType = BattType.None;
                        break;
                    case Unknown:
                        batteryType = BattType.Unknown;
                        break;
                    case Custom:
                        batteryType = BattType.Custom;
                        break;
                    case LG_RESU_6_4Ex:
                    case LG_RESU_3_3:
                    case LG_RESU_6_5:
                    case LG_RESU_10:
                    case LG_RESU_Plus:
                    case LG_M48063P3S:
                    case LG_M48126P3S:
                        batteryType = BattType.LG_Resu_V1;
                        break;
                    case Pylon_US2000A:
                    case Pylon_US2000B:
                        batteryType = BattType.Pylon_US2000A;
                        break;
                    case LeadAcid_Flooded:
                        batteryType = BattType.Lead_Acid_Flooded;
                        break;
                    case LeadAcid_Sealed:
                        batteryType = BattType.Lead_Acid_Sealed;
                        break;
                    case SimpliPHi_2_6:
                        batteryType = BattType.SimpliPHi_2_6;
                        break;
                    case SimpliPHi_3_4:
                        batteryType = BattType.SimpliPHi_3_4;
                        break;
                    case Redflow_V1:
                        batteryType = BattType.Redflow_V1;
                        break;
                    case Aquion_Aspen_48S_2_2:
                        batteryType = BattType.AquionAspen48S2_2;
                        break;
                    case Aquion_S20P:
                        batteryType = BattType.AquionS20P;
                        break;
                    case Aquion_S30:
                        batteryType = BattType.AquionS30;
                        break;
                }
            }
        }
    }

    /**
     * 电池充电设置类
     */
    @Data
    public static class BattChargeSettings implements Cloneable {

        /**
         * [Volts]
         */
        @JsonProperty("Voltage")
        private Double voltage;

        /**
         * [Amps]
         */
        @JsonProperty("Current")
        private Double current;

        @JsonProperty("VoltageValue")
        public double getVoltageValue() {
            return Optional.ofNullable(voltage).orElse(0.0);
        }

        @JsonProperty("CurrentValue")
        public double getCurrentValue() {
            return Optional.ofNullable(current).orElse(0.0);
        }

        @Override
        public Object clone() throws CloneNotSupportedException {
            return super.clone();
        }
    }

    /**
     * 电池浮充设置类
     */
    @Data
    public static class BattFloatSettings implements Cloneable {

        @JsonProperty("Voltage")
        private Double voltage; // range between 50 - 60 default 54

        @JsonProperty("Current")
        private Double current; // range between 0 - 50 default 3

        @JsonProperty("Time")
        private Integer time; // minutes between 0 and 60000

        @JsonProperty("index")
        private BatteryIndex index;

        @JsonProperty("VoltageValue")
        public double getVoltageValue() {
            return Optional.ofNullable(voltage).orElse(0.0);
        }

        @JsonProperty("CurrentValue")
        public double getCurrentValue() {
            return Optional.ofNullable(current).orElse(0.0);
        }

        @JsonProperty("TimeValue")
        public int getTimeValue() {
            return Optional.ofNullable(time).orElse(0);
        }

        @Override
        public Object clone() throws CloneNotSupportedException {
            return super.clone();
        }
    }

    public static enum FanMode
    {
        Auto,
        On,
        Off;

        @JsonValue
        public int toValue() {
            return this.ordinal();
        }

        @JsonCreator
        public static FanMode fromValue(Integer value) {
            if (value == null) {
                return null;
            }

            for (FanMode model : FanMode.values()) {
                if (model.ordinal() == value) {
                    return model;
                }
            }
            throw new IllegalArgumentException("No matching FanMode for value: " + value);
        }
    }


    public static enum BatteryManufacturer {
        None(0),
        Unknown(1),
        Custom(2),
        LG(300),
        Pylon(400),
        LeadAcid(500),
        SimpliPHi(600),
        Redflow(700),
        Aquion(800);

        @JsonProperty
        private final int value;

        BatteryManufacturer(int value) {
            this.value = value;
        }

        @JsonValue
        public int getValue() {
            return value;
        }

        @JSONCreator
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        public static BatteryManufacturer fromValue(Integer value) {
            if (value == null) {
                return null;
            }

            for (BatteryManufacturer manufacturer : BatteryManufacturer.values()) {
                if (manufacturer.getValue() == value) {
                    return manufacturer;
                }
            }
            throw new IllegalArgumentException("No matching BatteryManufacturer for value: " + value);
        }
    }

    public static enum BattType {
        None,               //selectable      // There has never been a battery detected
        Unknown,            // A battery was detected, but the BMS is somehow not responding
        Lead_Acid_Flooded,  // selectable
        Lead_Acid_Sealed,   // selectable
        SimpliPHi_2_6,          // selecteble (former Lithium Ion No BMS)
        Pylon_US2000A,
        Pylon_US2000B,
        Pylon_US2000BPlus,
        Pylon_US3000A,
        Pylon_US3000B,
        _Pylon_V2,
        LG_Resu_V1,
        _LG_V2,
        Redflow_V1,
        AquionS20P,         // selectable
        AquionS30,          // selectable
        AquionAspen48S2_2,  // selectable
        Custom,              // selectable
        SimpliPHi_3_4;


        @JsonValue
        public int getValue(){
            return  this.ordinal();
        }

        @JsonCreator
        public static BattType forValue(Integer value) {
            if (value == null) {
                return null;
            }

            for (BattType model : BattType.values()) {
                if (model.getValue() == value) {
                    return model;
                }
            }
            throw new IllegalArgumentException("No matching BattType for value: " + value);
        }
    }

    public static enum BatteryModel {
        None(0),
        Unknown(1),
        Custom(2),
        LG_RESU_6_4Ex(301),
        LG_RESU_3_3(302),
        LG_RESU_6_5(303),
        LG_RESU_10(304),
        LG_RESU_Plus(305),
        LG_M48063P3S(306),
        LG_M48126P3S(307),
        Pylon_US2000A(401),
        Pylon_US2000B(402),
        Pylon_US2000BPlus(403),
        Pylon_US3000A(404),
        Pylon_US3000B(405),
        LeadAcid_Flooded(501),
        LeadAcid_Sealed(502),
        SimpliPHi_2_6(601),
        SimpliPHi_3_4(602),
        Redflow_V1(701),
        Aquion_S20P(801),
        Aquion_S30(802),
        Aquion_Aspen_48S_2_2(803);

        @JsonProperty
        private final int value;

        BatteryModel(int value) {
            this.value = value;
        }

        @JsonValue
        public int getValue() {
            return value;
        }

        @JSONCreator
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        public static BatteryModel fromValue(Integer value) {
            if (value == null) {
                return null;
            }

            for (BatteryModel model : BatteryModel.values()) {
                if (model.getValue() == value) {
                    return model;
                }
            }
            throw new IllegalArgumentException("No matching BatteryModel for value: " + value);
        }
    }

    public static enum BatteryIndex {
        Lithium_Ion(0x00);

        private final int value;

        BatteryIndex(int value) {
            this.value = value;
        }

        @JsonValue
        public int getValue() {
            return value;
        }

        @JSONCreator
        @JsonCreator
        public static BatteryIndex fromValue(Integer value) {
            if (value == null) {
                return null;
            }

            for (BatteryIndex index : BatteryIndex.values()) {
                if (index.getValue() == value) {
                    return index;
                }
            }

            return null;
        }
    }

    public static enum Country {
        Italy(0x00),
        Czech(0x01),
        Germany(0x02),
        Spain(0x03),
        GreeceMainland(0x04),
        Denmark(0x05),
        Belgium(0x06),
        RomaniaSpecial(0x07),
        G83Spec(0x08),
        Australia(0x09),
        France(0x0a),
        China(0x0b),
        America(0x0c),
        Poland(0x0d),
        SouthAfrica(0x0e),
        Australia_L(0x0f),
        Brazil(0x10),
        ThailandM(0x11),
        ThailandP(0x12),
        Mauritius(0x13),
        Holland(0x14),
        Northern_Ireland(0x15),
        China_High_Pressure(0x16),
        France_50KhZ(0x17),
        France_60KhZ(0x18),
        Australia_Ergon(0x19),
        Australia_Energex(0x1a),
        Holland_16_or_20_Amp(0x1b),
        Korea(0x1c),
        China_Station(0x1d),
        Austria(0x1e),
        India(0x1f),
        Grid_Default_50Hz(0x20),
        Warehouse(0x21),
        Phillipines(0x22),
        Ireland(0x23),
        Taiwan(0x24),
        Bulgaria(0x25),
        Barbados(0x26),
        China_Max_Pressure(0x27),
        G59_3(0x28),
        Sweden(0x29),
        Chile(0x2a),
        Brazil_Low_Voltage(0x2b),
        New_Zealand(0x2c),
        IEEE1547_208Vac(0x2D),
        IEEE1547_220Vac(0x2E),
        IEEE1547_240Vac(0x2F),
        Default_60HzLowVolt(0x30),
        Default_50HzLowVolt(0x31),

        /**
         * Please note that this has different meaning in firmware 171709 and 151507
         *
         * In 151507 it's AustraliaWestern and in 171709 it's Western Power
         */
        AustraliaWestern(0x32),

        /**
         * Usable from 171709 + ROSS2.2
         */
        AUMicroGrid(0x33),
        Japan50Hz(0x34),
        Japan60Hz(0x35),
        IndiaHigher(0x36),
        DewaLowVolt(0x37),
        DewaMediumVolt(0x38),
        Slovakia(0x39),
        GreenGrid(0x3A),

        /**
         * Usable from 171709 + ROSS2.2
         */
        AUHorizon(0x44),
        Unknown(0xff); // Error message in the inverter

        private int value;

        Country(int value) {
            this.value = value;
        }

        @JsonValue
        public int getValue() {
            return value;
        }

        @JsonCreator
        public static Country fromValue(int value) {
            return forValue(value);
        }

        public static Country forValue(int value) {
            for (Country e : values()) {
                if (e.getValue() == value) {
                    return e;
                }
            }

            return null;
        }

        public static Object parse(byte value) {
            int unsignedByte = Byte.toUnsignedInt(value);
            Country o = forValue(unsignedByte);
            if (o != null) {
                return o;
            }

            return unsignedByte;
        }
    }


}
