package com.ebon.energy.fms.domain.vo.detail;

import com.ebon.energy.fms.common.enums.PhotoFeaturingEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.net.URI;
import java.time.Instant;
import java.util.UUID;

@Data
public class InstallationPhotoDescriptorDto {

    @JsonProperty("Id")
    private final UUID id;

    @JsonProperty("Featuring")
    private final PhotoFeaturingEnum featuring;

    @JsonProperty("DateCreated")
    private final Instant dateCreated;

    @JsonProperty("Uri")
    private final URI uri;

    @JsonProperty("ThumbnailUri")
    private final URI thumbnailUri;

    public InstallationPhotoDescriptorDto(UUID id,
                                          URI uri,
                                          URI thumbnailUri,
                                          PhotoFeaturingEnum featuring,
                                          Instant dateCreated) {
        this.id = id;
        this.uri = uri;
        this.thumbnailUri = thumbnailUri;
        this.featuring = featuring;
        this.dateCreated = dateCreated;
    }
}