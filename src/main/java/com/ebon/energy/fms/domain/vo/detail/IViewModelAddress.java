package com.ebon.energy.fms.domain.vo.detail;

import java.util.List;

public interface IViewModelAddress {
    String getPlaceId();
    void setPlaceId(String placeId);

    String getFullAddress();
    void setFullAddress(String fullAddress);

    String getLocalityString();
    void setLocalityString(String localityString);

    String getLatitude();
    void setLatitude(String latitude);

    String getLongitude();
    void setLongitude(String longitude);

    String getStreetNumber();
    void setStreetNumber(String streetNumber);

    String getStreetName();
    void setStreetName(String streetName);

    String getSuburb();
    void setSuburb(String suburb);

    String getState();
    void setState(String state);

    String getPostCode();
    void setPostCode(String postCode);

    String getCountry();
    void setCountry(String country);

    // 只读属性
    String getAddressString();

    String getTimeZoneId();
    void setTimeZoneId(String timeZoneId);
}