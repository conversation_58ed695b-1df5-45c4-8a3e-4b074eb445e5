package com.ebon.energy.fms.common.enums;

import lombok.Getter;

@Getter
public enum SolarPanelManufacturer {
    Other("Other", 23),
    Xtreme("Xtreme", 21),
    PhonoSolar("Phono Solar", 11),
    JinkoSolar("Jinko Solar", 8),
    LGSolar("LG Solar", 10),
    <PERSON><PERSON>("<PERSON><PERSON>", 22),
    FirstSolar("First Solar", 5),
    WINAICO("Winaico", 20),
    Kyocera("Kyocera", 9),
    <PERSON>sola("Renesola", 13),
    Trina("Trina", 19),
    CanadianSolar("Canadian Solar", 2),
    JASolar("JA Solar", 7),
    CNPV("CNPV", 3),
    RisenEnergy("Risen Energy", 14),
    ETSolar("ET Solar", 4),
    SolarFrontier("Solar Frontier", 15),
    HanwhaSolarOne("Hanwha SolarOne", 6),
    Solarworld("Solarworld", 16),
    BYD("BYD", 1),
    SUNPOWER("Sunpower", 17),
    SUNTECHPOWER("Suntech Power", 18),
    REC("REC", 12),
    None("Please select manufacturer", 0);

    private final String displayName;
    private final int order;

    SolarPanelManufacturer(String displayName, int order) {
        this.displayName = displayName;
        this.order = order;
    }
}