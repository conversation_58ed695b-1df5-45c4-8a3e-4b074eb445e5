package com.ebon.energy.fms.common.utils;

public final class AddressUtility {

    private AddressUtility() {
    }

    public static String compressStreetComponents(String streetNumber, String streetName) {
        String s1 = streetNumber == null ? "" : streetNumber;
        String s2 = streetName == null ? "" : streetName;
        return (s1 + " " + s2).trim();
    }
    private static boolean isNullOrEmpty(String s) {
        return s == null || s.isEmpty();
    }
}