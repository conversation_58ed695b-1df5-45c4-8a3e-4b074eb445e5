package com.ebon.energy.fms.common.enums;

import lombok.Getter;

@Getter
public enum PanelDirection {
    N("North", 1),
    E("East", 2),
    W("West", 3),
    S("South", 4),
    NE("North-East", 5),
    NW("North-West", 6),
    SE("South-East", 7),
    SW("South-West", 8),
    None("Please select direction", 0);

    private final String displayName;
    private final int order;

    PanelDirection(String displayName, int order) {
        this.displayName = displayName;
        this.order = order;
    }
}