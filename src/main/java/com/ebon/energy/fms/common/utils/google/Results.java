package com.ebon.energy.fms.common.utils.google;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Results {
    @JsonProperty("Formatted_Address")
    private String formattedAddress;

    @JsonProperty("Geometry")
    private Geometry geometry;

    @JsonProperty("Types")
    private String[] types;

    @JsonProperty("Address_Components")
    private AddressComponent[] addressComponents;

    @JsonProperty("Place_Id")
    private String placeId;
}
