package com.ebon.energy.fms.common.enums;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public enum PhotoFeaturingEnum {
    Other("Other"),
    Inverter("Inverter"),
    Switchboard("Switchboard"),
    SolarPanels("Solar Panels");

    @JsonProperty("Description")
    private final String description;

    PhotoFeaturingEnum(String description) {
        this.description = description;
    }
}