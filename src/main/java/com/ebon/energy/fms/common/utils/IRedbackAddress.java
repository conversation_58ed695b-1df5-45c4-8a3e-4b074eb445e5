// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.common.utils;

/**
 * Redback地址接口
 * 定义地址相关的基本属性和方法
 */
public interface IRedbackAddress {

    String getGooglePlaceId();
    void setGooglePlaceId(String googlePlaceId);

    String getAddressLineOne();
    void setAddressLineOne(String addressLineOne);

    String getAddressLineTwo();
    void setAddressLineTwo(String addressLineTwo);

    String getSuburb();
    void setSuburb(String suburb);

    String getState();
    void setState(String state);

    String getCountry();
    void setCountry(String country);

    String getPostCode();
    void setPostCode(String postCode);

    String getLatitude();
    void setLatitude(String latitude);

    String getLongitude();
    void setLongitude(String longitude);

    String getTimeZoneId();
    void setTimeZoneId(String timeZoneId);
}
