package com.ebon.energy.fms.common.utils;

/**
 * Google Maps时区API使用示例
 * 演示如何使用GoogleMapsTimeZoneApi获取时区信息
 */
public class TimeZoneApiExample {
    
    public static void main(String[] args) {
        // 注意：需要替换为实际的Google Maps API密钥
        String apiKey = "YOUR_GOOGLE_MAPS_API_KEY";
        
        // 创建API客户端实例
        GoogleMapsTimeZoneApi timeZoneApi = new GoogleMapsTimeZoneApi(apiKey);
        
        // 示例坐标 - 悉尼
        String sydneyLat = "-33.8688";
        String sydneyLon = "151.2093";
        
        System.out.println("获取悉尼的时区信息...");

        // 调用示例
        try {
            String windowsTimeZone = timeZoneApi.getTimeZone(sydneyLat, sydneyLon);
            System.out.println("悉尼的Windows时区ID: " + windowsTimeZone);
        } catch (Exception e) {
            System.err.println("获取时区信息失败: " + e.getMessage());
        }
        
        // 测试其他城市
        testOtherCities(timeZoneApi);
        
        // 测试IANA到Windows转换器
        testIanaToWindowsConverter();
    }
    
    /**
     * 测试其他城市的时区获取
     */
    private static void testOtherCities(GoogleMapsTimeZoneApi timeZoneApi) {
        System.out.println("\n=== 测试其他城市 ===");
        
        // 测试数据：城市名称、纬度、经度
        String[][] cities = {
            {"纽约", "40.7128", "-74.0060"},
            {"伦敦", "51.5074", "-0.1278"},
            {"东京", "35.6762", "139.6503"},
            {"北京", "39.9042", "116.4074"},
            {"洛杉矶", "34.0522", "-118.2437"}
        };
        
        for (String[] city : cities) {
            try {
                String windowsTimeZone = timeZoneApi.getTimeZone(city[1], city[2]);
                System.out.println(city[0] + " (" + city[1] + ", " + city[2] + ") -> " + windowsTimeZone);
            } catch (Exception e) {
                System.err.println("获取" + city[0] + "时区失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试IANA到Windows时区转换器
     */
    private static void testIanaToWindowsConverter() {
        System.out.println("\n=== 测试IANA到Windows时区转换 ===");
        
        String[] ianaTimeZones = {
            "America/New_York",
            "Europe/London", 
            "Asia/Tokyo",
            "Asia/Shanghai",
            "Australia/Sydney",
            "America/Los_Angeles",
            "Europe/Paris",
            "Invalid/TimeZone"
        };
        
        for (String ianaTimeZone : ianaTimeZones) {
            String windowsTimeZone = IanaToWindowsConverter.getWindowsIdForIana(ianaTimeZone);
            boolean isSupported = IanaToWindowsConverter.isSupported(ianaTimeZone);
            System.out.println(ianaTimeZone + " -> " + 
                (windowsTimeZone.isEmpty() ? "未找到映射" : windowsTimeZone) + 
                " (支持: " + isSupported + ")");
        }
        
        System.out.println("\n转换器支持的时区总数: " + IanaToWindowsConverter.getMappingCount());
    }
}
