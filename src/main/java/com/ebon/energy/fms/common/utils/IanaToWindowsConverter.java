package com.ebon.energy.fms.common.utils;

import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * IANA时区ID到Windows时区ID的转换器
 * 提供从IANA时区标识符到Windows时区标识符的映射
 */
public class IanaToWindowsConverter {
    
    // IANA到Windows时区的映射表
    private static final Map<String, String> IANA_TO_WINDOWS_MAP;
    
    static {
        Map<String, String> tempMap = new HashMap<>();
        
        // 基于常用的时区映射关系构建映射表
        // UTC相关
        tempMap.put("UTC", "UTC");
        tempMap.put("Etc/UTC", "UTC");
        tempMap.put("Etc/GMT", "GMT Standard Time");
        
        // 美洲时区
        tempMap.put("America/New_York", "Eastern Standard Time");
        tempMap.put("America/Detroit", "Eastern Standard Time");
        tempMap.put("America/Indiana/Indianapolis", "US Eastern Standard Time");
        tempMap.put("America/Chicago", "Central Standard Time");
        tempMap.put("America/Denver", "Mountain Standard Time");
        tempMap.put("America/Phoenix", "US Mountain Standard Time");
        tempMap.put("America/Los_Angeles", "Pacific Standard Time");
        tempMap.put("America/Anchorage", "Alaskan Standard Time");
        tempMap.put("Pacific/Honolulu", "Hawaiian Standard Time");
        tempMap.put("America/Adak", "Aleutian Standard Time");
        
        // 加拿大
        tempMap.put("America/Halifax", "Atlantic Standard Time");
        tempMap.put("America/St_Johns", "Newfoundland Standard Time");
        tempMap.put("America/Regina", "Canada Central Standard Time");
        tempMap.put("America/Winnipeg", "Central Standard Time");
        tempMap.put("America/Edmonton", "Mountain Standard Time");
        tempMap.put("America/Vancouver", "Pacific Standard Time");
        tempMap.put("America/Whitehorse", "Yukon Standard Time");
        
        // 墨西哥
        tempMap.put("America/Mexico_City", "Central Standard Time (Mexico)");
        tempMap.put("America/Cancun", "Eastern Standard Time (Mexico)");
        tempMap.put("America/Tijuana", "Pacific Standard Time (Mexico)");
        tempMap.put("America/Chihuahua", "Mountain Standard Time (Mexico)");
        
        // 南美洲
        tempMap.put("America/Sao_Paulo", "E. South America Standard Time");
        tempMap.put("America/Argentina/Buenos_Aires", "Argentina Standard Time");
        tempMap.put("America/Bogota", "SA Pacific Standard Time");
        tempMap.put("America/Lima", "SA Pacific Standard Time");
        tempMap.put("America/Santiago", "Pacific SA Standard Time");
        tempMap.put("America/Caracas", "Venezuela Standard Time");
        
        // 欧洲时区
        tempMap.put("Europe/London", "GMT Standard Time");
        tempMap.put("Europe/Dublin", "GMT Standard Time");
        tempMap.put("Europe/Paris", "W. Europe Standard Time");
        tempMap.put("Europe/Berlin", "W. Europe Standard Time");
        tempMap.put("Europe/Rome", "W. Europe Standard Time");
        tempMap.put("Europe/Madrid", "W. Europe Standard Time");
        tempMap.put("Europe/Amsterdam", "W. Europe Standard Time");
        tempMap.put("Europe/Brussels", "W. Europe Standard Time");
        tempMap.put("Europe/Vienna", "W. Europe Standard Time");
        tempMap.put("Europe/Zurich", "W. Europe Standard Time");
        tempMap.put("Europe/Stockholm", "W. Europe Standard Time");
        tempMap.put("Europe/Oslo", "W. Europe Standard Time");
        tempMap.put("Europe/Copenhagen", "W. Europe Standard Time");
        tempMap.put("Europe/Warsaw", "Central European Standard Time");
        tempMap.put("Europe/Prague", "Central European Standard Time");
        tempMap.put("Europe/Budapest", "Central European Standard Time");
        tempMap.put("Europe/Belgrade", "Central European Standard Time");
        tempMap.put("Europe/Athens", "GTB Standard Time");
        tempMap.put("Europe/Bucharest", "GTB Standard Time");
        tempMap.put("Europe/Sofia", "GTB Standard Time");
        tempMap.put("Europe/Helsinki", "FLE Standard Time");
        tempMap.put("Europe/Kiev", "FLE Standard Time");
        tempMap.put("Europe/Riga", "FLE Standard Time");
        tempMap.put("Europe/Tallinn", "FLE Standard Time");
        tempMap.put("Europe/Vilnius", "FLE Standard Time");
        tempMap.put("Europe/Moscow", "Russian Standard Time");
        tempMap.put("Europe/Istanbul", "Turkey Standard Time");
        
        // 亚洲时区
        tempMap.put("Asia/Shanghai", "China Standard Time");
        tempMap.put("Asia/Beijing", "China Standard Time");
        tempMap.put("Asia/Hong_Kong", "China Standard Time");
        tempMap.put("Asia/Taipei", "Taipei Standard Time");
        tempMap.put("Asia/Tokyo", "Tokyo Standard Time");
        tempMap.put("Asia/Seoul", "Korea Standard Time");
        tempMap.put("Asia/Singapore", "Singapore Standard Time");
        tempMap.put("Asia/Kuala_Lumpur", "Singapore Standard Time");
        tempMap.put("Asia/Bangkok", "SE Asia Standard Time");
        tempMap.put("Asia/Jakarta", "SE Asia Standard Time");
        tempMap.put("Asia/Manila", "Singapore Standard Time");
        tempMap.put("Asia/Kolkata", "India Standard Time");
        tempMap.put("Asia/Mumbai", "India Standard Time");
        tempMap.put("Asia/Dubai", "Arabian Standard Time");
        tempMap.put("Asia/Riyadh", "Arab Standard Time");
        tempMap.put("Asia/Tehran", "Iran Standard Time");
        tempMap.put("Asia/Jerusalem", "Israel Standard Time");
        
        // 澳洲和太平洋
        tempMap.put("Australia/Sydney", "AUS Eastern Standard Time");
        tempMap.put("Australia/Melbourne", "AUS Eastern Standard Time");
        tempMap.put("Australia/Brisbane", "E. Australia Standard Time");
        tempMap.put("Australia/Adelaide", "Cen. Australia Standard Time");
        tempMap.put("Australia/Perth", "W. Australia Standard Time");
        tempMap.put("Australia/Darwin", "AUS Central Standard Time");
        tempMap.put("Pacific/Auckland", "New Zealand Standard Time");
        tempMap.put("Pacific/Fiji", "Fiji Standard Time");
        
        // 非洲
        tempMap.put("Africa/Cairo", "Egypt Standard Time");
        tempMap.put("Africa/Johannesburg", "South Africa Standard Time");
        tempMap.put("Africa/Lagos", "W. Central Africa Standard Time");
        tempMap.put("Africa/Nairobi", "E. Africa Standard Time");
        
        IANA_TO_WINDOWS_MAP = Map.copyOf(tempMap);
    }
    
    /**
     * 根据IANA时区ID获取对应的Windows时区ID
     * 
     * @param ianaTimeZoneId IANA时区ID
     * @return Windows时区ID，如果找不到映射则返回空字符串
     */
    public static String getWindowsIdForIana(String ianaTimeZoneId) {
        if (ianaTimeZoneId == null || ianaTimeZoneId.trim().isEmpty()) {
            return "";
        }
        
        String windowsId = IANA_TO_WINDOWS_MAP.get(ianaTimeZoneId.trim());
        return windowsId != null ? windowsId : "";
    }
    
    /**
     * 检查是否支持指定的IANA时区ID
     * 
     * @param ianaTimeZoneId IANA时区ID
     * @return 如果支持返回true，否则返回false
     */
    public static boolean isSupported(String ianaTimeZoneId) {
        return ianaTimeZoneId != null && IANA_TO_WINDOWS_MAP.containsKey(ianaTimeZoneId.trim());
    }
    
    /**
     * 获取所有支持的IANA时区ID
     * 
     * @return 支持的IANA时区ID集合
     */
    public static java.util.Set<String> getSupportedIanaTimeZones() {
        return IANA_TO_WINDOWS_MAP.keySet();
    }
    
    /**
     * 获取映射表的大小
     * 
     * @return 映射表中的条目数量
     */
    public static int getMappingCount() {
        return IANA_TO_WINDOWS_MAP.size();
    }
}
