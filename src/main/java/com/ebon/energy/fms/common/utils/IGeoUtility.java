package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.domain.vo.detail.GeoCodeResult;
import com.ebon.energy.fms.domain.vo.detail.IViewModelAddress;

import javax.validation.ValidationException;

public interface IGeoUtility {

    IRedbackAddress geoCodeAddress(IViewModelAddress address) throws ValidationException;

    GeoCodeResult geoCodeAddress(String streetAddress, String suburb, String state, String country, String postcode)
            throws ValidationException;

    String timeZoneAddress(IRedbackAddress address) throws ValidationException;

    String timeZoneAddress(String latitude, String longitude) throws ValidationException;
}