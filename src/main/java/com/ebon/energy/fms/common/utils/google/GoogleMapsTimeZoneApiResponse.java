package com.ebon.energy.fms.common.utils.google;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * Google Maps时区API响应模型
 * 对应Google Maps Timezone API的JSON响应结构
 */
public class GoogleMapsTimeZoneApiResponse {
    
    /**
     * 时区偏移量（秒）
     */
    @JSONField(name = "dstOffset")
    private Integer dstOffset;
    
    /**
     * 原始偏移量（秒）
     */
    @JSONField(name = "rawOffset")
    private Integer rawOffset;
    
    /**
     * API请求状态
     */
    @JSONField(name = "status")
    private String status;
    
    /**
     * IANA时区ID
     */
    @JSONField(name = "timeZoneId")
    private String timeZoneId;
    
    /**
     * 时区名称
     */
    @JSONField(name = "timeZoneName")
    private String timeZoneName;
    
    /**
     * 错误消息（如果有）
     */
    @JSONField(name = "errorMessage")
    private String errorMessage;
    
    // Getters and Setters
    public Integer getDstOffset() {
        return dstOffset;
    }
    
    public void setDstOffset(Integer dstOffset) {
        this.dstOffset = dstOffset;
    }
    
    public Integer getRawOffset() {
        return rawOffset;
    }
    
    public void setRawOffset(Integer rawOffset) {
        this.rawOffset = rawOffset;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getTimeZoneId() {
        return timeZoneId;
    }
    
    public void setTimeZoneId(String timeZoneId) {
        this.timeZoneId = timeZoneId;
    }
    
    public String getTimeZoneName() {
        return timeZoneName;
    }
    
    public void setTimeZoneName(String timeZoneName) {
        this.timeZoneName = timeZoneName;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    @Override
    public String toString() {
        return "GoogleMapsTimeZoneApiResponse{" +
                "dstOffset=" + dstOffset +
                ", rawOffset=" + rawOffset +
                ", status='" + status + '\'' +
                ", timeZoneId='" + timeZoneId + '\'' +
                ", timeZoneName='" + timeZoneName + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
